# CSF Portal Backup and Recovery Procedures

This document outlines the backup and recovery procedures for the CSF Portal application.

## Table of Contents

- [Backup Strategy](#backup-strategy)
- [Database Backups](#database-backups)
- [File System Backups](#file-system-backups)
- [Configuration Backups](#configuration-backups)
- [Recovery Procedures](#recovery-procedures)
- [Backup Verification](#backup-verification)
- [Disaster Recovery Plan](#disaster-recovery-plan)

## Backup Strategy

The CSF Portal backup strategy follows the 3-2-1 rule:
- 3 copies of data
- 2 different storage types
- 1 off-site backup

### Backup Schedule

| Data Type | Frequency | Retention |
|-----------|-----------|-----------|
| Database | Daily | 30 days |
| Database | Weekly | 3 months |
| Database | Monthly | 1 year |
| File System | Weekly | 3 months |
| Configuration | After changes | Keep last 5 versions |

## Database Backups

### Automated MongoDB Backups

1. Install the required tools:

```bash
sudo apt-get install -y mongodb-clients
```

2. Create a backup script at `/usr/local/bin/mongodb-backup.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/var/backups/mongodb"
MONGODB_HOST="localhost"
MONGODB_PORT="27017"
MONGODB_USER="backup_user"
MONGODB_PASSWORD="backup_password"
MONGODB_DATABASE="csfportal"
DATE=$(date +"%Y-%m-%d")
BACKUP_PATH="$BACKUP_DIR/$DATE"
LOG_FILE="/var/log/mongodb-backup.log"

# Create backup directory
mkdir -p $BACKUP_PATH

# Log start
echo "$(date) - Starting MongoDB backup" >> $LOG_FILE

# Perform backup
mongodump --host $MONGODB_HOST --port $MONGODB_PORT \
  --username $MONGODB_USER --password $MONGODB_PASSWORD \
  --authenticationDatabase admin \
  --db $MONGODB_DATABASE \
  --out $BACKUP_PATH \
  >> $LOG_FILE 2>&1

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "$(date) - MongoDB backup completed successfully" >> $LOG_FILE
  
  # Compress backup
  tar -czf $BACKUP_PATH.tar.gz -C $BACKUP_DIR $DATE
  rm -rf $BACKUP_PATH
  
  # Remove backups older than 30 days
  find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +30 -delete
else
  echo "$(date) - MongoDB backup failed" >> $LOG_FILE
fi
```

3. Make the script executable:

```bash
sudo chmod +x /usr/local/bin/mongodb-backup.sh
```

4. Create a MongoDB backup user:

```bash
mongosh -u admin -p secure_password --authenticationDatabase admin
```

```javascript
use admin
db.createUser({
  user: "backup_user",
  pwd: "backup_password",
  roles: [
    { role: "backup", db: "admin" },
    { role: "readWrite", db: "csfportal" }
  ]
})
exit
```

5. Set up a cron job to run the backup script daily:

```bash
sudo crontab -e
```

Add the following line:

```
0 2 * * * /usr/local/bin/mongodb-backup.sh
```

### Weekly and Monthly Backups

Create additional scripts for weekly and monthly backups with different retention periods:

1. Create `/usr/local/bin/mongodb-backup-weekly.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/var/backups/mongodb/weekly"
MONGODB_HOST="localhost"
MONGODB_PORT="27017"
MONGODB_USER="backup_user"
MONGODB_PASSWORD="backup_password"
MONGODB_DATABASE="csfportal"
DATE=$(date +"%Y-%m-%d")
BACKUP_PATH="$BACKUP_DIR/$DATE"
LOG_FILE="/var/log/mongodb-backup-weekly.log"

# Create backup directory
mkdir -p $BACKUP_PATH

# Log start
echo "$(date) - Starting MongoDB weekly backup" >> $LOG_FILE

# Perform backup
mongodump --host $MONGODB_HOST --port $MONGODB_PORT \
  --username $MONGODB_USER --password $MONGODB_PASSWORD \
  --authenticationDatabase admin \
  --db $MONGODB_DATABASE \
  --out $BACKUP_PATH \
  >> $LOG_FILE 2>&1

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "$(date) - MongoDB weekly backup completed successfully" >> $LOG_FILE
  
  # Compress backup
  tar -czf $BACKUP_PATH.tar.gz -C $BACKUP_DIR $DATE
  rm -rf $BACKUP_PATH
  
  # Remove backups older than 90 days
  find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +90 -delete
else
  echo "$(date) - MongoDB weekly backup failed" >> $LOG_FILE
fi
```

2. Create `/usr/local/bin/mongodb-backup-monthly.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/var/backups/mongodb/monthly"
MONGODB_HOST="localhost"
MONGODB_PORT="27017"
MONGODB_USER="backup_user"
MONGODB_PASSWORD="backup_password"
MONGODB_DATABASE="csfportal"
DATE=$(date +"%Y-%m-%d")
BACKUP_PATH="$BACKUP_DIR/$DATE"
LOG_FILE="/var/log/mongodb-backup-monthly.log"

# Create backup directory
mkdir -p $BACKUP_PATH

# Log start
echo "$(date) - Starting MongoDB monthly backup" >> $LOG_FILE

# Perform backup
mongodump --host $MONGODB_HOST --port $MONGODB_PORT \
  --username $MONGODB_USER --password $MONGODB_PASSWORD \
  --authenticationDatabase admin \
  --db $MONGODB_DATABASE \
  --out $BACKUP_PATH \
  >> $LOG_FILE 2>&1

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "$(date) - MongoDB monthly backup completed successfully" >> $LOG_FILE
  
  # Compress backup
  tar -czf $BACKUP_PATH.tar.gz -C $BACKUP_DIR $DATE
  rm -rf $BACKUP_PATH
  
  # Remove backups older than 365 days
  find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +365 -delete
else
  echo "$(date) - MongoDB monthly backup failed" >> $LOG_FILE
fi
```

3. Make the scripts executable:

```bash
sudo chmod +x /usr/local/bin/mongodb-backup-weekly.sh
sudo chmod +x /usr/local/bin/mongodb-backup-monthly.sh
```

4. Set up cron jobs:

```bash
sudo crontab -e
```

Add the following lines:

```
0 2 * * 0 /usr/local/bin/mongodb-backup-weekly.sh
0 3 1 * * /usr/local/bin/mongodb-backup-monthly.sh
```

## File System Backups

### Application Code and Assets

1. Create a backup script at `/usr/local/bin/app-backup.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/var/backups/csfportal"
APP_DIR="/var/www/csfportal"
DATE=$(date +"%Y-%m-%d")
BACKUP_FILE="$BACKUP_DIR/csfportal-$DATE.tar.gz"
LOG_FILE="/var/log/app-backup.log"

# Create backup directory
mkdir -p $BACKUP_DIR

# Log start
echo "$(date) - Starting application backup" >> $LOG_FILE

# Perform backup
tar -czf $BACKUP_FILE -C $APP_DIR . \
  --exclude="node_modules" \
  --exclude="client/node_modules" \
  --exclude="client/build" \
  --exclude="logs" \
  >> $LOG_FILE 2>&1

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "$(date) - Application backup completed successfully" >> $LOG_FILE
  
  # Remove backups older than 90 days
  find $BACKUP_DIR -name "csfportal-*.tar.gz" -type f -mtime +90 -delete
else
  echo "$(date) - Application backup failed" >> $LOG_FILE
fi
```

2. Make the script executable:

```bash
sudo chmod +x /usr/local/bin/app-backup.sh
```

3. Set up a cron job to run the backup script weekly:

```bash
sudo crontab -e
```

Add the following line:

```
0 4 * * 0 /usr/local/bin/app-backup.sh
```

## Configuration Backups

### Server Configuration Files

1. Create a backup script at `/usr/local/bin/config-backup.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/var/backups/config"
DATE=$(date +"%Y-%m-%d")
BACKUP_FILE="$BACKUP_DIR/config-$DATE.tar.gz"
LOG_FILE="/var/log/config-backup.log"

# Create backup directory
mkdir -p $BACKUP_DIR

# Log start
echo "$(date) - Starting configuration backup" >> $LOG_FILE

# Perform backup
tar -czf $BACKUP_FILE \
  /etc/nginx/sites-available/csfportal \
  /etc/nginx/sites-available/grafana \
  /etc/systemd/system/csfportal.service \
  /etc/systemd/system/node_exporter.service \
  /etc/systemd/system/prometheus.service \
  /etc/systemd/system/alertmanager.service \
  /etc/systemd/system/mongodb_exporter.service \
  /etc/prometheus/prometheus.yml \
  /etc/prometheus/alert.rules.yml \
  /etc/alertmanager/alertmanager.yml \
  /var/www/csfportal/.env \
  >> $LOG_FILE 2>&1

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "$(date) - Configuration backup completed successfully" >> $LOG_FILE
  
  # Keep only the last 5 backups
  ls -t $BACKUP_DIR/config-*.tar.gz | tail -n +6 | xargs -r rm
else
  echo "$(date) - Configuration backup failed" >> $LOG_FILE
fi
```

2. Make the script executable:

```bash
sudo chmod +x /usr/local/bin/config-backup.sh
```

3. Set up a cron job to run after configuration changes:

```bash
sudo crontab -e
```

Add the following line:

```
0 5 * * * /usr/local/bin/config-backup.sh
```

## Off-site Backups

### Automated Transfer to Remote Storage

1. Install rclone:

```bash
curl https://rclone.org/install.sh | sudo bash
```

2. Configure rclone with your preferred cloud storage provider:

```bash
rclone config
```

Follow the prompts to set up a remote called "offsite".

3. Create a script to transfer backups to off-site storage:

```bash
#!/bin/bash

# Configuration
BACKUP_DIRS=("/var/backups/mongodb" "/var/backups/mongodb/weekly" "/var/backups/mongodb/monthly" "/var/backups/csfportal" "/var/backups/config")
REMOTE_NAME="offsite"
REMOTE_PATH="csfportal-backups"
LOG_FILE="/var/log/offsite-backup.log"

# Log start
echo "$(date) - Starting off-site backup transfer" >> $LOG_FILE

# Transfer each backup directory
for DIR in "${BACKUP_DIRS[@]}"; do
  echo "$(date) - Transferring $DIR to off-site storage" >> $LOG_FILE
  
  rclone sync $DIR $REMOTE_NAME:$REMOTE_PATH/$(basename $DIR) \
    --progress \
    >> $LOG_FILE 2>&1
  
  if [ $? -eq 0 ]; then
    echo "$(date) - Transfer of $DIR completed successfully" >> $LOG_FILE
  else
    echo "$(date) - Transfer of $DIR failed" >> $LOG_FILE
  fi
done

echo "$(date) - Off-site backup transfer completed" >> $LOG_FILE
```

4. Make the script executable:

```bash
sudo chmod +x /usr/local/bin/offsite-backup.sh
```

5. Set up a cron job to run daily:

```bash
sudo crontab -e
```

Add the following line:

```
0 6 * * * /usr/local/bin/offsite-backup.sh
```

## Recovery Procedures

### Database Recovery

1. To restore a MongoDB database from backup:

```bash
# Extract the backup
tar -xzf /var/backups/mongodb/YYYY-MM-DD.tar.gz -C /tmp

# Restore the database
mongorestore --host localhost --port 27017 \
  --username admin --password secure_password \
  --authenticationDatabase admin \
  --db csfportal \
  --drop \
  /tmp/YYYY-MM-DD/csfportal
```

### Application Recovery

1. To restore the application code:

```bash
# Stop the application
sudo systemctl stop csfportal

# Extract the backup
tar -xzf /var/backups/csfportal/csfportal-YYYY-MM-DD.tar.gz -C /tmp/csfportal-restore

# Restore the application files
sudo rsync -av --delete /tmp/csfportal-restore/ /var/www/csfportal/

# Install dependencies
cd /var/www/csfportal
npm install
npm run client-install

# Build the client
npm run build --prefix client

# Start the application
sudo systemctl start csfportal
```

### Configuration Recovery

1. To restore configuration files:

```bash
# Extract the backup
tar -xzf /var/backups/config/config-YYYY-MM-DD.tar.gz -C /tmp

# Restore configuration files
sudo cp /tmp/etc/nginx/sites-available/csfportal /etc/nginx/sites-available/
sudo cp /tmp/etc/nginx/sites-available/grafana /etc/nginx/sites-available/
sudo cp /tmp/etc/systemd/system/csfportal.service /etc/systemd/system/
sudo cp /tmp/etc/systemd/system/node_exporter.service /etc/systemd/system/
sudo cp /tmp/etc/systemd/system/prometheus.service /etc/systemd/system/
sudo cp /tmp/etc/systemd/system/alertmanager.service /etc/systemd/system/
sudo cp /tmp/etc/systemd/system/mongodb_exporter.service /etc/systemd/system/
sudo cp /tmp/etc/prometheus/prometheus.yml /etc/prometheus/
sudo cp /tmp/etc/prometheus/alert.rules.yml /etc/prometheus/
sudo cp /tmp/etc/alertmanager/alertmanager.yml /etc/alertmanager/
sudo cp /tmp/var/www/csfportal/.env /var/www/csfportal/

# Reload services
sudo systemctl daemon-reload
sudo systemctl restart nginx
sudo systemctl restart csfportal
sudo systemctl restart prometheus
sudo systemctl restart alertmanager
```

## Backup Verification

### Automated Verification

1. Create a verification script at `/usr/local/bin/verify-backups.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIRS=("/var/backups/mongodb" "/var/backups/mongodb/weekly" "/var/backups/mongodb/monthly" "/var/backups/csfportal" "/var/backups/config")
LOG_FILE="/var/log/backup-verification.log"
EMAIL="<EMAIL>"

# Log start
echo "$(date) - Starting backup verification" >> $LOG_FILE

# Check if backups exist and are recent
for DIR in "${BACKUP_DIRS[@]}"; do
  echo "$(date) - Checking $DIR" >> $LOG_FILE
  
  # Find the most recent backup
  LATEST_BACKUP=$(find $DIR -name "*.tar.gz" -type f -printf "%T@ %p\n" | sort -n | tail -1 | cut -d' ' -f2-)
  
  if [ -z "$LATEST_BACKUP" ]; then
    echo "$(date) - No backups found in $DIR" >> $LOG_FILE
    echo "No backups found in $DIR" | mail -s "Backup Verification Failed" $EMAIL
    continue
  fi
  
  # Check if the backup is recent (less than 48 hours old)
  BACKUP_TIME=$(stat -c %Y "$LATEST_BACKUP")
  CURRENT_TIME=$(date +%s)
  DIFF_HOURS=$(( ($CURRENT_TIME - $BACKUP_TIME) / 3600 ))
  
  echo "$(date) - Latest backup in $DIR is $DIFF_HOURS hours old" >> $LOG_FILE
  
  if [ $DIFF_HOURS -gt 48 ]; then
    echo "$(date) - Backup in $DIR is too old ($DIFF_HOURS hours)" >> $LOG_FILE
    echo "Backup in $DIR is too old ($DIFF_HOURS hours)" | mail -s "Backup Verification Failed" $EMAIL
    continue
  fi
  
  # Verify the integrity of the backup
  tar -tzf "$LATEST_BACKUP" > /dev/null 2>&1
  
  if [ $? -ne 0 ]; then
    echo "$(date) - Backup $LATEST_BACKUP is corrupted" >> $LOG_FILE
    echo "Backup $LATEST_BACKUP is corrupted" | mail -s "Backup Verification Failed" $EMAIL
    continue
  fi
  
  echo "$(date) - Backup $LATEST_BACKUP is valid" >> $LOG_FILE
done

echo "$(date) - Backup verification completed" >> $LOG_FILE
```

2. Make the script executable:

```bash
sudo chmod +x /usr/local/bin/verify-backups.sh
```

3. Set up a cron job to run daily:

```bash
sudo crontab -e
```

Add the following line:

```
0 7 * * * /usr/local/bin/verify-backups.sh
```

### Manual Verification

Perform a manual verification of backups quarterly:

1. Restore a database backup to a test environment
2. Restore an application backup to a test environment
3. Verify that the application works correctly
4. Document the results of the verification

## Disaster Recovery Plan

### Complete Server Failure

In case of a complete server failure, follow these steps to recover:

1. Provision a new server with the same specifications
2. Install the required software (Node.js, MongoDB, Nginx, etc.)
3. Restore configuration files from backup
4. Restore the application code from backup
5. Restore the database from backup
6. Verify that the application is working correctly

### Data Corruption

In case of data corruption:

1. Identify the extent of the corruption
2. Stop the application to prevent further damage
3. Restore the database from the most recent backup before the corruption
4. Verify that the application is working correctly
5. Analyze the cause of the corruption and take preventive measures

### Security Breach

In case of a security breach:

1. Isolate the affected server
2. Assess the extent of the breach
3. Restore the system from backups to a clean state
4. Apply all security patches
5. Change all passwords and access credentials
6. Verify that the application is working correctly
7. Monitor the system for any signs of continued breach

### Recovery Time Objectives (RTO)

| Scenario | Target RTO |
|----------|------------|
| Database Recovery | 2 hours |
| Application Recovery | 4 hours |
| Complete Server Failure | 8 hours |
| Data Corruption | 4 hours |
| Security Breach | 12 hours |

### Recovery Point Objectives (RPO)

| Scenario | Target RPO |
|----------|------------|
| Database | 24 hours |
| Application Code | 1 week |
| Configuration | After each change |

## Backup Responsibilities

| Role | Responsibilities |
|------|------------------|
| System Administrator | Implement and maintain backup systems |
| Database Administrator | Verify database backups |
| Application Developer | Ensure application can be restored from backups |
| Security Officer | Verify security of backup systems |
| IT Manager | Overall responsibility for backup strategy |