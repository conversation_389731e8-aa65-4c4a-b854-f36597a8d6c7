const CanvasAPI = require('../integrations/canvas/canvasAPI');
const CanvasConfig = require('../../models/CanvasConfig');

// Initialize Canvas API with empty credentials (will be updated when needed)
let canvasAPI = new CanvasAPI('', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await CanvasConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      canvasAPI = new CanvasAPI(config.domain, config.apiKey);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Canvas configuration:', error);
    throw error;
  }
};

/**
 * Get courses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCourses = async (req, res) => {
  try {
    await getLatestConfig();
    const courses = await canvasAPI.getCourses(req.query);
    res.json(courses);
  } catch (error) {
    console.error('Controller error fetching Canvas courses:', error);
    res.status(500).json({ message: 'Error fetching Canvas courses', error: error.message });
  }
};

/**
 * Get course details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCourse = async (req, res) => {
  try {
    await getLatestConfig();
    const course = await canvasAPI.getCourse(req.params.id, req.query);
    res.json(course);
  } catch (error) {
    console.error(`Controller error fetching Canvas course ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching Canvas course ${req.params.id}`, error: error.message });
  }
};

/**
 * Get course modules
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCourseModules = async (req, res) => {
  try {
    await getLatestConfig();
    const modules = await canvasAPI.getCourseModules(req.params.id, req.query);
    res.json(modules);
  } catch (error) {
    console.error(`Controller error fetching Canvas course ${req.params.id} modules:`, error);
    res.status(500).json({ message: `Error fetching Canvas course ${req.params.id} modules`, error: error.message });
  }
};

/**
 * Get course assignments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCourseAssignments = async (req, res) => {
  try {
    await getLatestConfig();
    const assignments = await canvasAPI.getCourseAssignments(req.params.id, req.query);
    res.json(assignments);
  } catch (error) {
    console.error(`Controller error fetching Canvas course ${req.params.id} assignments:`, error);
    res.status(500).json({ message: `Error fetching Canvas course ${req.params.id} assignments`, error: error.message });
  }
};

/**
 * Get course files
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCourseFiles = async (req, res) => {
  try {
    await getLatestConfig();
    const files = await canvasAPI.getCourseFiles(req.params.id, req.query);
    res.json(files);
  } catch (error) {
    console.error(`Controller error fetching Canvas course ${req.params.id} files:`, error);
    res.status(500).json({ message: `Error fetching Canvas course ${req.params.id} files`, error: error.message });
  }
};

/**
 * Get file details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFile = async (req, res) => {
  try {
    await getLatestConfig();
    const file = await canvasAPI.getFile(req.params.id, req.query);
    res.json(file);
  } catch (error) {
    console.error(`Controller error fetching Canvas file ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching Canvas file ${req.params.id}`, error: error.message });
  }
};

/**
 * Get users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    await getLatestConfig();
    const users = await canvasAPI.getUsers(req.query);
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching Canvas users:', error);
    res.status(500).json({ message: 'Error fetching Canvas users', error: error.message });
  }
};

/**
 * Get user details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUser = async (req, res) => {
  try {
    await getLatestConfig();
    const user = await canvasAPI.getUser(req.params.id, req.query);
    res.json(user);
  } catch (error) {
    console.error(`Controller error fetching Canvas user ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching Canvas user ${req.params.id}`, error: error.message });
  }
};

/**
 * Save Canvas configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { domain, apiKey } = req.body;

    if (!domain || !apiKey) {
      return res.status(400).json({ message: 'Domain and API Key are required' });
    }

    // Create a new configuration
    const config = new CanvasConfig({
      domain,
      apiKey
    });

    await config.save();

    // Update the API instance with new credentials
    canvasAPI = new CanvasAPI(domain, apiKey);

    res.json({ message: 'Canvas configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Canvas configuration:', error);
    res.status(500).json({ message: 'Error saving Canvas configuration', error: error.message });
  }
};

/**
 * Get Canvas configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Canvas configuration not found' });
    }

    // Don't send the actual API key back to the client for security
    res.json({
      domain: config.domain,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Canvas configuration:', error);
    res.status(500).json({ message: 'Error fetching Canvas configuration', error: error.message });
  }
};