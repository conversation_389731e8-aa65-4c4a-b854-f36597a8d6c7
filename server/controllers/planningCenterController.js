const PlanningCenterAPI = require('../integrations/planningCenter/planningCenterAPI');
const PlanningCenterConfig = require('../../models/PlanningCenterConfig');

// Initialize Planning Center API with empty credentials (will be updated when needed)
let planningCenterAPI = new PlanningCenterAPI('', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await PlanningCenterConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      planningCenterAPI = new PlanningCenterAPI(config.clientId, config.clientSecret);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Planning Center configuration:', error);
    throw error;
  }
};

/**
 * Get all Planning Center applications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getApplications = async (req, res) => {
  try {
    const applications = await planningCenterAPI.getApplications();
    res.json(applications);
  } catch (error) {
    console.error('Controller error fetching Planning Center applications:', error);
    res.status(500).json({ message: 'Error fetching Planning Center applications', error: error.message });
  }
};

/**
 * Get Planning Center events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    const events = await planningCenterAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching Planning Center events:', error);
    res.status(500).json({ message: 'Error fetching Planning Center events', error: error.message });
  }
};

/**
 * Get Planning Center people
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPeople = async (req, res) => {
  try {
    const people = await planningCenterAPI.getPeople(req.query);
    res.json(people);
  } catch (error) {
    console.error('Controller error fetching Planning Center people:', error);
    res.status(500).json({ message: 'Error fetching Planning Center people', error: error.message });
  }
};

/**
 * Get Planning Center resources
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getResources = async (req, res) => {
  try {
    const resources = await planningCenterAPI.getResources(req.query);
    res.json(resources);
  } catch (error) {
    console.error('Controller error fetching Planning Center resources:', error);
    res.status(500).json({ message: 'Error fetching Planning Center resources', error: error.message });
  }
};

/**
 * Save Planning Center configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { clientId, clientSecret } = req.body;

    if (!clientId || !clientSecret) {
      return res.status(400).json({ message: 'Client ID and Client Secret are required' });
    }

    // Create a new configuration
    const config = new PlanningCenterConfig({
      clientId,
      clientSecret
    });

    await config.save();

    // Update the API instance with new credentials
    planningCenterAPI = new PlanningCenterAPI(clientId, clientSecret);

    res.json({ message: 'Planning Center configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Planning Center configuration:', error);
    res.status(500).json({ message: 'Error saving Planning Center configuration', error: error.message });
  }
};

/**
 * Get Planning Center configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Planning Center configuration not found' });
    }

    // Don't send the actual client secret back to the client for security
    res.json({
      clientId: config.clientId,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Planning Center configuration:', error);
    res.status(500).json({ message: 'Error fetching Planning Center configuration', error: error.message });
  }
};
