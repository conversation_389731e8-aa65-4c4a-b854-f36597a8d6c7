const GoogleDriveAPI = require('../integrations/googleDrive/googleDriveAPI');
const GoogleDriveConfig = require('../../models/GoogleDriveConfig');
const path = require('path');
const fs = require('fs');

// Initialize Google Drive API with empty credentials (will be updated when needed)
let googleDriveAPI = null;

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await GoogleDriveConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      const tokenPath = path.resolve(process.cwd(), config.tokenPath || './google-drive-token.json');
      googleDriveAPI = new GoogleDriveAPI(
        config.clientId,
        config.clientSecret,
        config.redirectUri,
        tokenPath
      );
      await googleDriveAPI.initialize();
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Google Drive configuration:', error);
    throw error;
  }
};

/**
 * Save Google Drive configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { clientId, clientSecret, redirectUri, tokenPath } = req.body;

    if (!clientId || !clientSecret || !redirectUri) {
      return res.status(400).json({ message: 'Client ID, Client Secret, and Redirect URI are required' });
    }

    // Create a new configuration
    const config = new GoogleDriveConfig({
      clientId,
      clientSecret,
      redirectUri,
      tokenPath: tokenPath || './google-drive-token.json'
    });

    await config.save();

    // Update the API instance with new credentials
    const resolvedTokenPath = path.resolve(process.cwd(), config.tokenPath);
    googleDriveAPI = new GoogleDriveAPI(
      clientId,
      clientSecret,
      redirectUri,
      resolvedTokenPath
    );
    await googleDriveAPI.initialize();

    res.json({ message: 'Google Drive configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Google Drive configuration:', error);
    res.status(500).json({ message: 'Error saving Google Drive configuration', error: error.message });
  }
};

/**
 * Get Google Drive configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    // Don't send the actual client secret back to the client for security
    res.json({
      clientId: config.clientId,
      redirectUri: config.redirectUri,
      tokenPath: config.tokenPath,
      configuredAt: config.updatedAt,
      isAuthenticated: googleDriveAPI ? googleDriveAPI.isAuthenticated() : false
    });
  } catch (error) {
    console.error('Error fetching Google Drive configuration:', error);
    res.status(500).json({ message: 'Error fetching Google Drive configuration', error: error.message });
  }
};

/**
 * Get authentication URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAuthUrl = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    const authUrl = googleDriveAPI.getAuthUrl();
    res.json({ authUrl });
  } catch (error) {
    console.error('Error generating Google Drive auth URL:', error);
    res.status(500).json({ message: 'Error generating Google Drive auth URL', error: error.message });
  }
};

/**
 * Handle OAuth2 callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code } = req.query;
    
    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    await googleDriveAPI.getToken(code);
    res.json({ message: 'Google Drive authentication successful' });
  } catch (error) {
    console.error('Error handling Google Drive callback:', error);
    res.status(500).json({ message: 'Error handling Google Drive callback', error: error.message });
  }
};

/**
 * List files in Google Drive
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listFiles = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    if (!googleDriveAPI.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Drive',
        authUrl: googleDriveAPI.getAuthUrl()
      });
    }

    const options = req.query;
    const files = await googleDriveAPI.listFiles(options);
    res.json(files);
  } catch (error) {
    console.error('Error listing files from Google Drive:', error);
    res.status(500).json({ message: 'Error listing files from Google Drive', error: error.message });
  }
};

/**
 * Search files in Google Drive
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchFiles = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    if (!googleDriveAPI.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Drive',
        authUrl: googleDriveAPI.getAuthUrl()
      });
    }

    const { query, ...options } = req.query;
    
    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    const files = await googleDriveAPI.searchFiles(query, options);
    res.json(files);
  } catch (error) {
    console.error('Error searching files in Google Drive:', error);
    res.status(500).json({ message: 'Error searching files in Google Drive', error: error.message });
  }
};

/**
 * Get file metadata
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFile = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    if (!googleDriveAPI.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Drive',
        authUrl: googleDriveAPI.getAuthUrl()
      });
    }

    const { fileId } = req.params;
    
    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    const file = await googleDriveAPI.getFile(fileId);
    res.json(file);
  } catch (error) {
    console.error('Error getting file from Google Drive:', error);
    res.status(500).json({ message: 'Error getting file from Google Drive', error: error.message });
  }
};

/**
 * Get embedded viewer URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEmbeddedViewerUrl = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Google Drive configuration not found' });
    }

    const { fileId } = req.params;
    
    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    const viewerUrl = googleDriveAPI.getEmbeddedViewerUrl(fileId);
    res.json({ viewerUrl });
  } catch (error) {
    console.error('Error getting embedded viewer URL:', error);
    res.status(500).json({ message: 'Error getting embedded viewer URL', error: error.message });
  }
};