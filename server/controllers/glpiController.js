const GLPIAPI = require('../integrations/glpi/glpiAPI');
const GLPIConfig = require('../../models/GLPIConfig');

// Initialize GLPI API with empty credentials (will be updated when needed)
let glpiAPI = null;

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await GLPIConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      glpiAPI = new GLPIAPI(
        config.url,
        config.appToken,
        config.userToken
      );
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching GLPI configuration:', error);
    throw error;
  }
};

/**
 * Save GLPI configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { url, appToken, username, password, userToken } = req.body;

    if (!url || !appToken || (!username && !password && !userToken)) {
      return res.status(400).json({ message: 'URL, App Token, and either Username/Password or User Token are required' });
    }

    // Create a new configuration
    const config = new GLPIConfig({
      url,
      appToken,
      username: username || '',
      password: password || '',
      userToken: userToken || null
    });

    await config.save();

    // Update the API instance with new credentials
    glpiAPI = new GLPIAPI(
      url,
      appToken,
      userToken
    );

    res.json({ message: 'GLPI configuration saved successfully' });
  } catch (error) {
    console.error('Error saving GLPI configuration:', error);
    res.status(500).json({ message: 'Error saving GLPI configuration', error: error.message });
  }
};

/**
 * Get GLPI configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Don't send the actual password or tokens back to the client for security
    res.json({
      url: config.url,
      username: config.username,
      hasPassword: !!config.password,
      hasUserToken: !!config.userToken,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching GLPI configuration:', error);
    res.status(500).json({ message: 'Error fetching GLPI configuration', error: error.message });
  }
};

/**
 * Test GLPI connection
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.testConnection = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Try to initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Get profile information to verify connection
    const profileInfo = await glpiAPI.getMyProfiles();
    
    // Kill session
    await glpiAPI.killSession();

    res.json({ 
      message: 'GLPI connection successful',
      profileInfo
    });
  } catch (error) {
    console.error('Error testing GLPI connection:', error);
    res.status(500).json({ message: 'Error testing GLPI connection', error: error.message });
  }
};

/**
 * Get assets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssets = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Get assets
    const assets = await glpiAPI.getAssets(req.query);
    
    // Kill session
    await glpiAPI.killSession();

    res.json(assets);
  } catch (error) {
    console.error('Error getting GLPI assets:', error);
    res.status(500).json({ message: 'Error getting GLPI assets', error: error.message });
  }
};

/**
 * Get asset by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAsset = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ message: 'Asset ID is required' });
    }

    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Get asset
    const asset = await glpiAPI.getAsset(id);
    
    // Kill session
    await glpiAPI.killSession();

    res.json(asset);
  } catch (error) {
    console.error('Error getting GLPI asset:', error);
    res.status(500).json({ message: 'Error getting GLPI asset', error: error.message });
  }
};

/**
 * Search assets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchAssets = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Search assets
    const results = await glpiAPI.searchAssets(req.query);
    
    // Kill session
    await glpiAPI.killSession();

    res.json(results);
  } catch (error) {
    console.error('Error searching GLPI assets:', error);
    res.status(500).json({ message: 'Error searching GLPI assets', error: error.message });
  }
};

/**
 * Get asset types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetTypes = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Get asset types
    const types = await glpiAPI.getAssetTypes();
    
    // Kill session
    await glpiAPI.killSession();

    res.json(types);
  } catch (error) {
    console.error('Error getting GLPI asset types:', error);
    res.status(500).json({ message: 'Error getting GLPI asset types', error: error.message });
  }
};

/**
 * Get asset models
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetModels = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Get asset models
    const models = await glpiAPI.getAssetModels();
    
    // Kill session
    await glpiAPI.killSession();

    res.json(models);
  } catch (error) {
    console.error('Error getting GLPI asset models:', error);
    res.status(500).json({ message: 'Error getting GLPI asset models', error: error.message });
  }
};

/**
 * Get asset manufacturers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetManufacturers = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Get asset manufacturers
    const manufacturers = await glpiAPI.getAssetManufacturers();
    
    // Kill session
    await glpiAPI.killSession();

    res.json(manufacturers);
  } catch (error) {
    console.error('Error getting GLPI asset manufacturers:', error);
    res.status(500).json({ message: 'Error getting GLPI asset manufacturers', error: error.message });
  }
};

/**
 * Create asset
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createAsset = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Create asset
    const asset = await glpiAPI.createAsset(req.body);
    
    // Kill session
    await glpiAPI.killSession();

    res.json(asset);
  } catch (error) {
    console.error('Error creating GLPI asset:', error);
    res.status(500).json({ message: 'Error creating GLPI asset', error: error.message });
  }
};

/**
 * Update asset
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateAsset = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ message: 'Asset ID is required' });
    }

    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Update asset
    const asset = await glpiAPI.updateAsset(id, req.body);
    
    // Kill session
    await glpiAPI.killSession();

    res.json(asset);
  } catch (error) {
    console.error('Error updating GLPI asset:', error);
    res.status(500).json({ message: 'Error updating GLPI asset', error: error.message });
  }
};

/**
 * Delete asset
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteAsset = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ message: 'Asset ID is required' });
    }

    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Initialize session
    if (config.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else {
      await glpiAPI.initSessionByCredentials(config.username, config.password);
    }

    // Delete asset
    await glpiAPI.deleteAsset(id);
    
    // Kill session
    await glpiAPI.killSession();

    res.json({ message: 'Asset deleted successfully' });
  } catch (error) {
    console.error('Error deleting GLPI asset:', error);
    res.status(500).json({ message: 'Error deleting GLPI asset', error: error.message });
  }
};