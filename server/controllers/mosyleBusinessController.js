const MosyleBusinessAPI = require('../integrations/mosyleBusiness/mosyleBusinessAPI');
const MosyleBusinessConfig = require('../../models/MosyleBusinessConfig');

// Initialize Mosyle Business API with empty credentials (will be updated when needed)
let mosyleBusinessAPI = new MosyleBusinessAPI('', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await MosyleBusinessConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      mosyleBusinessAPI = new MosyleBusinessAPI(config.apiKey, config.domain);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Mosyle Business configuration:', error);
    throw error;
  }
};

/**
 * Get all Mosyle Business devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig();
    const devices = await mosyleBusinessAPI.getDevices(req.query);
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business devices:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business devices', error: error.message });
  }
};

/**
 * Get Mosyle Business device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const deviceDetails = await mosyleBusinessAPI.getDeviceDetails(deviceId);
    res.json(deviceDetails);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business device details:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business device details', error: error.message });
  }
};

/**
 * Get all Mosyle Business users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    await getLatestConfig();
    const users = await mosyleBusinessAPI.getUsers(req.query);
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business users:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business users', error: error.message });
  }
};

/**
 * Get all Mosyle Business groups
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getGroups = async (req, res) => {
  try {
    await getLatestConfig();
    const groups = await mosyleBusinessAPI.getGroups(req.query);
    res.json(groups);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business groups:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business groups', error: error.message });
  }
};

/**
 * Save Mosyle Business configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { apiKey, domain } = req.body;

    if (!apiKey || !domain) {
      return res.status(400).json({ message: 'API Key and Domain are required' });
    }

    // Create a new configuration
    const config = new MosyleBusinessConfig({
      apiKey,
      domain
    });

    await config.save();

    // Update the API instance with new credentials
    mosyleBusinessAPI = new MosyleBusinessAPI(apiKey, domain);

    res.json({ message: 'Mosyle Business configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Mosyle Business configuration:', error);
    res.status(500).json({ message: 'Error saving Mosyle Business configuration', error: error.message });
  }
};

/**
 * Get Mosyle Business configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Mosyle Business configuration not found' });
    }

    // Don't send the actual API key back to the client for security
    res.json({
      domain: config.domain,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Mosyle Business configuration:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business configuration', error: error.message });
  }
};