const DreoAPI = require('../integrations/dreo/dreoAPI');
const DreoConfig = require('../../models/DreoConfig');

// Initialize Dreo API with empty credentials (will be updated when needed)
let dreoAPI = new DreoAPI('', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await DreoConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      dreoAPI = new DreoAPI(config.username, config.password);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Dreo configuration:', error);
    throw error;
  }
};

/**
 * Get all Dreo devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig();
    const devices = await dreoAPI.getDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Dreo devices:', error);
    res.status(500).json({ message: 'Error fetching Dreo devices', error: error.message });
  }
};

/**
 * Get Dreo device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const deviceDetails = await dreoAPI.getDeviceDetails(deviceId);
    res.json(deviceDetails);
  } catch (error) {
    console.error('Controller error fetching Dreo device details:', error);
    res.status(500).json({ message: 'Error fetching Dreo device details', error: error.message });
  }
};

/**
 * Set device power state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setPower = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const { power } = req.body;
    
    if (power === undefined) {
      return res.status(400).json({ message: 'Power state is required' });
    }
    
    const result = await dreoAPI.setPower(deviceId, power);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device power:', error);
    res.status(500).json({ message: 'Error setting Dreo device power', error: error.message });
  }
};

/**
 * Set device temperature
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setTemperature = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const { temperature } = req.body;
    
    if (temperature === undefined) {
      return res.status(400).json({ message: 'Temperature is required' });
    }
    
    const result = await dreoAPI.setTemperature(deviceId, temperature);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device temperature:', error);
    res.status(500).json({ message: 'Error setting Dreo device temperature', error: error.message });
  }
};

/**
 * Set device fan speed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setFanSpeed = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const { speed } = req.body;
    
    if (speed === undefined) {
      return res.status(400).json({ message: 'Fan speed is required' });
    }
    
    const result = await dreoAPI.setFanSpeed(deviceId, speed);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device fan speed:', error);
    res.status(500).json({ message: 'Error setting Dreo device fan speed', error: error.message });
  }
};

/**
 * Set device mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setMode = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const { mode } = req.body;
    
    if (mode === undefined) {
      return res.status(400).json({ message: 'Mode is required' });
    }
    
    const result = await dreoAPI.setMode(deviceId, mode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device mode:', error);
    res.status(500).json({ message: 'Error setting Dreo device mode', error: error.message });
  }
};

/**
 * Save Dreo configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ message: 'Username and Password are required' });
    }

    // Create a new configuration
    const config = new DreoConfig({
      username,
      password
    });

    await config.save();

    // Update the API instance with new credentials
    dreoAPI = new DreoAPI(username, password);

    res.json({ message: 'Dreo configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Dreo configuration:', error);
    res.status(500).json({ message: 'Error saving Dreo configuration', error: error.message });
  }
};

/**
 * Get Dreo configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Dreo configuration not found' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      username: config.username,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Dreo configuration:', error);
    res.status(500).json({ message: 'Error fetching Dreo configuration', error: error.message });
  }
};