const LenelS2NetBoxAPI = require('../integrations/lenelS2NetBox/lenelS2NetBoxAPI');
const LenelS2NetBoxConfig = require('../../models/LenelS2NetBoxConfig');

// Initialize Lenel S2 NetBox API with empty credentials (will be updated when needed)
let lenelS2NetBoxAPI = new LenelS2NetBoxAPI('', '', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await LenelS2NetBoxConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      lenelS2NetBoxAPI = new LenelS2NetBoxAPI(config.host, config.username, config.password, config.port);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Lenel S2 NetBox configuration:', error);
    throw error;
  }
};

/**
 * Get all Lenel S2 NetBox access points
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessPoints = async (req, res) => {
  try {
    await getLatestConfig();
    const accessPoints = await lenelS2NetBoxAPI.getAccessPoints();
    res.json(accessPoints);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox access points:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox access points', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox access point details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessPointDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const accessPointId = req.params.id;
    const accessPointDetails = await lenelS2NetBoxAPI.getAccessPointDetails(accessPointId);
    res.json(accessPointDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox access point details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox access point details', error: error.message });
  }
};

/**
 * Get all Lenel S2 NetBox cardholders
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCardholders = async (req, res) => {
  try {
    await getLatestConfig();
    const cardholders = await lenelS2NetBoxAPI.getCardholders();
    res.json(cardholders);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox cardholders:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox cardholders', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox cardholder details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCardholderDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const cardholderId = req.params.id;
    const cardholderDetails = await lenelS2NetBoxAPI.getCardholderDetails(cardholderId);
    res.json(cardholderDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox cardholder details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox cardholder details', error: error.message });
  }
};

/**
 * Get all Lenel S2 NetBox alarms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAlarms = async (req, res) => {
  try {
    await getLatestConfig();
    const alarms = await lenelS2NetBoxAPI.getAlarms(req.query);
    res.json(alarms);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox alarms:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox alarms', error: error.message });
  }
};

/**
 * Acknowledge a Lenel S2 NetBox alarm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.acknowledgeAlarm = async (req, res) => {
  try {
    await getLatestConfig();
    const alarmId = req.params.id;
    const result = await lenelS2NetBoxAPI.acknowledgeAlarm(alarmId);
    res.json(result);
  } catch (error) {
    console.error('Controller error acknowledging Lenel S2 NetBox alarm:', error);
    res.status(500).json({ message: 'Error acknowledging Lenel S2 NetBox alarm', error: error.message });
  }
};

/**
 * Get all Lenel S2 NetBox events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    await getLatestConfig();
    const events = await lenelS2NetBoxAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox events:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox events', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    await getLatestConfig();
    const status = await lenelS2NetBoxAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox system status:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox system status', error: error.message });
  }
};

/**
 * Unlock a Lenel S2 NetBox door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unlockDoor = async (req, res) => {
  try {
    await getLatestConfig();
    const doorId = req.params.id;
    const result = await lenelS2NetBoxAPI.unlockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unlocking Lenel S2 NetBox door:', error);
    res.status(500).json({ message: 'Error unlocking Lenel S2 NetBox door', error: error.message });
  }
};

/**
 * Lock a Lenel S2 NetBox door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.lockDoor = async (req, res) => {
  try {
    await getLatestConfig();
    const doorId = req.params.id;
    const result = await lenelS2NetBoxAPI.lockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error locking Lenel S2 NetBox door:', error);
    res.status(500).json({ message: 'Error locking Lenel S2 NetBox door', error: error.message });
  }
};

/**
 * Save Lenel S2 NetBox configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { host, username, password, port, localNetwork } = req.body;

    if (!host || !username || !password) {
      return res.status(400).json({ message: 'Host, Username, and Password are required' });
    }

    // Create a new configuration
    const config = new LenelS2NetBoxConfig({
      host,
      username,
      password,
      port: port || 443,
      localNetwork: localNetwork !== undefined ? localNetwork : true
    });

    await config.save();

    // Update the API instance with new credentials
    lenelS2NetBoxAPI = new LenelS2NetBoxAPI(host, username, password, port || 443);

    res.json({ message: 'Lenel S2 NetBox configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Lenel S2 NetBox configuration:', error);
    res.status(500).json({ message: 'Error saving Lenel S2 NetBox configuration', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Lenel S2 NetBox configuration not found' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      username: config.username,
      port: config.port,
      localNetwork: config.localNetwork,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Lenel S2 NetBox configuration:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox configuration', error: error.message });
  }
};