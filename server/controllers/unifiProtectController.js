const UnifiProtectAPI = require('../integrations/unifiProtect/unifiProtectAPI');
const UnifiProtectConfig = require('../../models/UnifiProtectConfig');

// Initialize UniFi Protect API with empty credentials (will be updated when needed)
let unifiProtectAPI = new UnifiProtectAPI('', '', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await UnifiProtectConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      unifiProtectAPI = new UnifiProtectAPI(config.host, config.username, config.password, config.port);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching UniFi Protect configuration:', error);
    throw error;
  }
};

/**
 * Get all UniFi Protect cameras
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameras = async (req, res) => {
  try {
    await getLatestConfig();
    const cameras = await unifiProtectAPI.getCameras();
    res.json(cameras);
  } catch (error) {
    console.error('Controller error fetching UniFi Protect cameras:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect cameras', error: error.message });
  }
};

/**
 * Get UniFi Protect camera details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const cameraId = req.params.id;
    const cameraDetails = await unifiProtectAPI.getCameraDetails(cameraId);
    res.json(cameraDetails);
  } catch (error) {
    console.error('Controller error fetching UniFi Protect camera details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect camera details', error: error.message });
  }
};

/**
 * Get UniFi Protect camera snapshot
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraSnapshot = async (req, res) => {
  try {
    await getLatestConfig();
    const cameraId = req.params.id;
    const snapshot = await unifiProtectAPI.getCameraSnapshot(cameraId);
    
    // Set appropriate headers for image data
    res.set('Content-Type', 'image/jpeg');
    res.send(snapshot);
  } catch (error) {
    console.error('Controller error fetching UniFi Protect camera snapshot:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect camera snapshot', error: error.message });
  }
};

/**
 * Get UniFi Protect events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    await getLatestConfig();
    const events = await unifiProtectAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching UniFi Protect events:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect events', error: error.message });
  }
};

/**
 * Get UniFi Protect system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    await getLatestConfig();
    const status = await unifiProtectAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching UniFi Protect system status:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect system status', error: error.message });
  }
};

/**
 * Save UniFi Protect configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { host, username, password, port } = req.body;

    if (!host || !username || !password) {
      return res.status(400).json({ message: 'Host, Username, and Password are required' });
    }

    // Create a new configuration
    const config = new UnifiProtectConfig({
      host,
      username,
      password,
      port: port || 443
    });

    await config.save();

    // Update the API instance with new credentials
    unifiProtectAPI = new UnifiProtectAPI(host, username, password, port || 443);

    res.json({ message: 'UniFi Protect configuration saved successfully' });
  } catch (error) {
    console.error('Error saving UniFi Protect configuration:', error);
    res.status(500).json({ message: 'Error saving UniFi Protect configuration', error: error.message });
  }
};

/**
 * Get UniFi Protect configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'UniFi Protect configuration not found' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      username: config.username,
      port: config.port,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching UniFi Protect configuration:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect configuration', error: error.message });
  }
};