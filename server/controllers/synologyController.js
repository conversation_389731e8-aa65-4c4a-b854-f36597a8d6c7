const SynologyAPI = require('../integrations/synology/synologyAPI');
const SynologyConfig = require('../../models/SynologyConfig');

// Initialize Synology API with empty credentials (will be updated when needed)
let synologyAPI = null;

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await SynologyConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      synologyAPI = new SynologyAPI(
        config.host,
        config.port,
        config.username,
        config.password,
        config.secure
      );
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Synology configuration:', error);
    throw error;
  }
};

/**
 * Save Synology configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { host, port, username, password, secure } = req.body;

    if (!host || !port || !username || !password) {
      return res.status(400).json({ message: 'Host, port, username, and password are required' });
    }

    // Create a new configuration
    const config = new SynologyConfig({
      host,
      port: parseInt(port, 10),
      username,
      password,
      secure: secure !== undefined ? secure : true
    });

    await config.save();

    // Update the API instance with new credentials
    synologyAPI = new SynologyAPI(
      host,
      parseInt(port, 10),
      username,
      password,
      secure !== undefined ? secure : true
    );

    res.json({ message: 'Synology configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Synology configuration:', error);
    res.status(500).json({ message: 'Error saving Synology configuration', error: error.message });
  }
};

/**
 * Get Synology configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      port: config.port,
      username: config.username,
      secure: config.secure,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Synology configuration:', error);
    res.status(500).json({ message: 'Error fetching Synology configuration', error: error.message });
  }
};

/**
 * List files in a directory
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listFiles = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    const { path, ...options } = req.query;
    
    if (!path) {
      return res.status(400).json({ message: 'Path parameter is required' });
    }

    const files = await synologyAPI.listFiles(path, options);
    res.json(files);
  } catch (error) {
    console.error('Error listing files from Synology:', error);
    res.status(500).json({ message: 'Error listing files from Synology', error: error.message });
  }
};

/**
 * Download a file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.downloadFile = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    const { path } = req.query;
    
    if (!path) {
      return res.status(400).json({ message: 'Path parameter is required' });
    }

    const fileData = await synologyAPI.downloadFile(path);
    
    // Get the filename from the path
    const filename = path.split('/').pop();
    
    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    
    res.send(fileData);
  } catch (error) {
    console.error('Error downloading file from Synology:', error);
    res.status(500).json({ message: 'Error downloading file from Synology', error: error.message });
  }
};

/**
 * Create a sharing link
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createSharingLink = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    const { path, ...options } = req.body;
    
    if (!path) {
      return res.status(400).json({ message: 'Path parameter is required' });
    }

    const sharingInfo = await synologyAPI.createSharingLink(path, options);
    res.json(sharingInfo);
  } catch (error) {
    console.error('Error creating sharing link on Synology:', error);
    res.status(500).json({ message: 'Error creating sharing link on Synology', error: error.message });
  }
};