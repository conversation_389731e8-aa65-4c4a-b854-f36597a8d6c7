const CanvaAPI = require('../integrations/canva/canvaAPI');
const CanvaConfig = require('../../models/CanvaConfig');

// Initialize Canva API with empty credentials (will be updated when needed)
let canvaAPI = new CanvaAPI('', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await CanvaConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      canvaAPI = new CanvaAPI(config.domain, config.apiKey);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Canva configuration:', error);
    throw error;
  }
};

/**
 * Get designs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesigns = async (req, res) => {
  try {
    await getLatestConfig();
    const designs = await canvaAPI.getDesigns(req.query);
    res.json(designs);
  } catch (error) {
    console.error('Controller error fetching Canva designs:', error);
    res.status(500).json({ message: 'Error fetching Canva designs', error: error.message });
  }
};

/**
 * Get design details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesign = async (req, res) => {
  try {
    await getLatestConfig();
    const design = await canvaAPI.getDesign(req.params.id, req.query);
    res.json(design);
  } catch (error) {
    console.error(`Controller error fetching Canva design ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching Canva design ${req.params.id}`, error: error.message });
  }
};

/**
 * Get templates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTemplates = async (req, res) => {
  try {
    await getLatestConfig();
    const templates = await canvaAPI.getTemplates(req.query);
    res.json(templates);
  } catch (error) {
    console.error('Controller error fetching Canva templates:', error);
    res.status(500).json({ message: 'Error fetching Canva templates', error: error.message });
  }
};

/**
 * Get design assets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesignAssets = async (req, res) => {
  try {
    await getLatestConfig();
    const assets = await canvaAPI.getDesignAssets(req.params.id, req.query);
    res.json(assets);
  } catch (error) {
    console.error(`Controller error fetching Canva design ${req.params.id} assets:`, error);
    res.status(500).json({ message: `Error fetching Canva design ${req.params.id} assets`, error: error.message });
  }
};

/**
 * Get design files
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesignFiles = async (req, res) => {
  try {
    await getLatestConfig();
    const files = await canvaAPI.getDesignFiles(req.params.id, req.query);
    res.json(files);
  } catch (error) {
    console.error(`Controller error fetching Canva design ${req.params.id} files:`, error);
    res.status(500).json({ message: `Error fetching Canva design ${req.params.id} files`, error: error.message });
  }
};

/**
 * Get file details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFile = async (req, res) => {
  try {
    await getLatestConfig();
    const file = await canvaAPI.getFile(req.params.id, req.query);
    res.json(file);
  } catch (error) {
    console.error(`Controller error fetching Canva file ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching Canva file ${req.params.id}`, error: error.message });
  }
};

/**
 * Get users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    await getLatestConfig();
    const users = await canvaAPI.getUsers(req.query);
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching Canva users:', error);
    res.status(500).json({ message: 'Error fetching Canva users', error: error.message });
  }
};

/**
 * Get user details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUser = async (req, res) => {
  try {
    await getLatestConfig();
    const user = await canvaAPI.getUser(req.params.id, req.query);
    res.json(user);
  } catch (error) {
    console.error(`Controller error fetching Canva user ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching Canva user ${req.params.id}`, error: error.message });
  }
};

/**
 * Save Canva configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { domain, apiKey } = req.body;

    if (!domain || !apiKey) {
      return res.status(400).json({ message: 'Domain and API Key are required' });
    }

    // Create a new configuration
    const config = new CanvaConfig({
      domain,
      apiKey
    });

    await config.save();

    // Update the API instance with new credentials
    canvaAPI = new CanvaAPI(domain, apiKey);

    res.json({ message: 'Canva configuration saved successfully' });
  } catch (error) {
    console.error('Error saving Canva configuration:', error);
    res.status(500).json({ message: 'Error saving Canva configuration', error: error.message });
  }
};

/**
 * Get Canva configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Canva configuration not found' });
    }

    // Don't send the actual API key back to the client for security
    res.json({
      domain: config.domain,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching Canva configuration:', error);
    res.status(500).json({ message: 'Error fetching Canva configuration', error: error.message });
  }
};