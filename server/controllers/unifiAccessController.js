const UnifiAccessAPI = require('../integrations/unifiAccess/unifiAccessAPI');
const UnifiAccessConfig = require('../../models/UnifiAccessConfig');

// Initialize UniFi Access API with empty credentials (will be updated when needed)
let unifiAccessAPI = new UnifiAccessAPI('', '', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await UnifiAccessConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      unifiAccessAPI = new UnifiAccessAPI(config.host, config.username, config.password, config.port);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching UniFi Access configuration:', error);
    throw error;
  }
};

/**
 * Get all UniFi Access doors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoors = async (req, res) => {
  try {
    await getLatestConfig();
    const doors = await unifiAccessAPI.getDoors();
    res.json(doors);
  } catch (error) {
    console.error('Controller error fetching UniFi Access doors:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access doors', error: error.message });
  }
};

/**
 * Get UniFi Access door details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoorDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const doorId = req.params.id;
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    res.json(doorDetails);
  } catch (error) {
    console.error('Controller error fetching UniFi Access door details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access door details', error: error.message });
  }
};

/**
 * Unlock a door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unlockDoor = async (req, res) => {
  try {
    await getLatestConfig();
    const doorId = req.params.id;
    const result = await unifiAccessAPI.unlockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unlocking UniFi Access door:', error);
    res.status(500).json({ message: 'Error unlocking UniFi Access door', error: error.message });
  }
};

/**
 * Lock a door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.lockDoor = async (req, res) => {
  try {
    await getLatestConfig();
    const doorId = req.params.id;
    const result = await unifiAccessAPI.lockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error locking UniFi Access door:', error);
    res.status(500).json({ message: 'Error locking UniFi Access door', error: error.message });
  }
};

/**
 * Get all UniFi Access access points
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessPoints = async (req, res) => {
  try {
    await getLatestConfig();
    const accessPoints = await unifiAccessAPI.getAccessPoints();
    res.json(accessPoints);
  } catch (error) {
    console.error('Controller error fetching UniFi Access access points:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access access points', error: error.message });
  }
};

/**
 * Get all UniFi Access users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    await getLatestConfig();
    const users = await unifiAccessAPI.getUsers();
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching UniFi Access users:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access users', error: error.message });
  }
};

/**
 * Get UniFi Access events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    await getLatestConfig();
    const events = await unifiAccessAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching UniFi Access events:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access events', error: error.message });
  }
};

/**
 * Get UniFi Access system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    await getLatestConfig();
    const status = await unifiAccessAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching UniFi Access system status:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access system status', error: error.message });
  }
};

/**
 * Save UniFi Access configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { host, username, password, port } = req.body;

    if (!host || !username || !password) {
      return res.status(400).json({ message: 'Host, Username, and Password are required' });
    }

    // Create a new configuration
    const config = new UnifiAccessConfig({
      host,
      username,
      password,
      port: port || 443
    });

    await config.save();

    // Update the API instance with new credentials
    unifiAccessAPI = new UnifiAccessAPI(host, username, password, port || 443);

    res.json({ message: 'UniFi Access configuration saved successfully' });
  } catch (error) {
    console.error('Error saving UniFi Access configuration:', error);
    res.status(500).json({ message: 'Error saving UniFi Access configuration', error: error.message });
  }
};

/**
 * Get UniFi Access configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'UniFi Access configuration not found' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      username: config.username,
      port: config.port,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching UniFi Access configuration:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access configuration', error: error.message });
  }
};