const UnifiNetworkAPI = require('../integrations/unifiNetwork/unifiNetworkAPI');
const UnifiNetworkConfig = require('../../models/UnifiNetworkConfig');

// Initialize UniFi Network API with empty credentials (will be updated when needed)
let unifiNetworkAPI = new UnifiNetworkAPI('', '', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    const config = await UnifiNetworkConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      unifiNetworkAPI = new UnifiNetworkAPI(config.host, config.username, config.password, config.port);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching UniFi Network configuration:', error);
    throw error;
  }
};

/**
 * Get all UniFi Network devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig();
    const devices = await unifiNetworkAPI.getDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching UniFi Network devices:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network devices', error: error.message });
  }
};

/**
 * Get UniFi Network device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const deviceDetails = await unifiNetworkAPI.getDeviceDetails(deviceId);
    res.json(deviceDetails);
  } catch (error) {
    console.error('Controller error fetching UniFi Network device details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network device details', error: error.message });
  }
};

/**
 * Get all UniFi Network clients
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getClients = async (req, res) => {
  try {
    await getLatestConfig();
    const clients = await unifiNetworkAPI.getClients();
    res.json(clients);
  } catch (error) {
    console.error('Controller error fetching UniFi Network clients:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network clients', error: error.message });
  }
};

/**
 * Get UniFi Network client details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getClientDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const clientId = req.params.id;
    const clientDetails = await unifiNetworkAPI.getClientDetails(clientId);
    res.json(clientDetails);
  } catch (error) {
    console.error('Controller error fetching UniFi Network client details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network client details', error: error.message });
  }
};

/**
 * Block a UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.blockClient = async (req, res) => {
  try {
    await getLatestConfig();
    const clientId = req.params.id;
    const result = await unifiNetworkAPI.blockClient(clientId);
    res.json(result);
  } catch (error) {
    console.error('Controller error blocking UniFi Network client:', error);
    res.status(500).json({ message: 'Error blocking UniFi Network client', error: error.message });
  }
};

/**
 * Unblock a UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unblockClient = async (req, res) => {
  try {
    await getLatestConfig();
    const clientId = req.params.id;
    const result = await unifiNetworkAPI.unblockClient(clientId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unblocking UniFi Network client:', error);
    res.status(500).json({ message: 'Error unblocking UniFi Network client', error: error.message });
  }
};

/**
 * Get UniFi Network statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getNetworkStats = async (req, res) => {
  try {
    await getLatestConfig();
    const stats = await unifiNetworkAPI.getNetworkStats();
    res.json(stats);
  } catch (error) {
    console.error('Controller error fetching UniFi Network statistics:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network statistics', error: error.message });
  }
};

/**
 * Get UniFi Network site information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSiteInfo = async (req, res) => {
  try {
    await getLatestConfig();
    const siteInfo = await unifiNetworkAPI.getSiteInfo();
    res.json(siteInfo);
  } catch (error) {
    console.error('Controller error fetching UniFi Network site information:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network site information', error: error.message });
  }
};

/**
 * Save UniFi Network configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { host, username, password, port, site } = req.body;

    if (!host || !username || !password) {
      return res.status(400).json({ message: 'Host, Username, and Password are required' });
    }

    // Create a new configuration
    const config = new UnifiNetworkConfig({
      host,
      username,
      password,
      port: port || 443
    });

    await config.save();

    // Update the API instance with new credentials
    unifiNetworkAPI = new UnifiNetworkAPI(host, username, password, port || 443, site || 'default');

    res.json({ message: 'UniFi Network configuration saved successfully' });
  } catch (error) {
    console.error('Error saving UniFi Network configuration:', error);
    res.status(500).json({ message: 'Error saving UniFi Network configuration', error: error.message });
  }
};

/**
 * Get UniFi Network configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'UniFi Network configuration not found' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      username: config.username,
      port: config.port,
      configuredAt: config.updatedAt
    });
  } catch (error) {
    console.error('Error fetching UniFi Network configuration:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network configuration', error: error.message });
  }
};