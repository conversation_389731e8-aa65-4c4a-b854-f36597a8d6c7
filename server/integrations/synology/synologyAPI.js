// Synology API Wrapper
const axios = require('axios');
const querystring = require('querystring');

/**
 * Synology API Wrapper
 * Documentation: https://global.download.synology.com/download/Document/Software/DeveloperGuide/Package/FileStation/All/enu/Synology_File_Station_API_Guide.pdf
 */
class SynologyAPI {
  constructor(host, port, username, password, secure = true) {
    this.host = host;
    this.port = port;
    this.username = username;
    this.password = password;
    this.secure = secure;
    this.baseURL = `${secure ? 'https' : 'http'}://${host}:${port}/webapi`;
    this.sid = null;
    this.axios = axios.create({
      baseURL: this.baseURL
    });
  }

  /**
   * Login to Synology DiskStation
   * @returns {Promise<string>} Session ID
   */
  async login() {
    try {
      const params = {
        api: 'SYNO.API.Auth',
        version: '3',
        method: 'login',
        account: this.username,
        passwd: this.password,
        session: 'FileStation',
        format: 'sid'
      };

      const response = await this.axios.get('/auth.cgi', { params });
      
      if (response.data.success) {
        this.sid = response.data.data.sid;
        return this.sid;
      } else {
        throw new Error(response.data.error.code);
      }
    } catch (error) {
      console.error('Error logging in to Synology:', error);
      throw error;
    }
  }

  /**
   * Logout from Synology DiskStation
   * @returns {Promise<boolean>} Success status
   */
  async logout() {
    try {
      if (!this.sid) {
        return true;
      }

      const params = {
        api: 'SYNO.API.Auth',
        version: '1',
        method: 'logout',
        session: 'FileStation',
        _sid: this.sid
      };

      const response = await this.axios.get('/auth.cgi', { params });
      
      if (response.data.success) {
        this.sid = null;
        return true;
      } else {
        throw new Error(response.data.error.code);
      }
    } catch (error) {
      console.error('Error logging out from Synology:', error);
      throw error;
    }
  }

  /**
   * Get file list
   * @param {string} folderPath Path to the folder
   * @param {Object} options Additional options
   * @returns {Promise<Array>} List of files
   */
  async listFiles(folderPath, options = {}) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const params = {
        api: 'SYNO.FileStation.List',
        version: '2',
        method: 'list',
        folder_path: folderPath,
        _sid: this.sid,
        ...options
      };

      const response = await this.axios.get('/entry.cgi', { params });
      
      if (response.data.success) {
        return response.data.data.files;
      } else {
        throw new Error(response.data.error.code);
      }
    } catch (error) {
      console.error('Error listing files from Synology:', error);
      throw error;
    }
  }

  /**
   * Download a file
   * @param {string} path Path to the file
   * @returns {Promise<Buffer>} File data
   */
  async downloadFile(path) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const params = {
        api: 'SYNO.FileStation.Download',
        version: '2',
        method: 'download',
        path,
        _sid: this.sid
      };

      const response = await this.axios.get('/entry.cgi', { 
        params,
        responseType: 'arraybuffer'
      });
      
      return response.data;
    } catch (error) {
      console.error('Error downloading file from Synology:', error);
      throw error;
    }
  }

  /**
   * Upload a file
   * @param {string} folderPath Destination folder path
   * @param {Buffer} fileData File data
   * @param {string} fileName File name
   * @returns {Promise<Object>} Upload result
   */
  async uploadFile(folderPath, fileData, fileName) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const formData = new FormData();
      formData.append('api', 'SYNO.FileStation.Upload');
      formData.append('version', '2');
      formData.append('method', 'upload');
      formData.append('path', folderPath);
      formData.append('create_parents', 'true');
      formData.append('_sid', this.sid);
      formData.append('file', new Blob([fileData]), fileName);

      const response = await this.axios.post('/entry.cgi', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error.code);
      }
    } catch (error) {
      console.error('Error uploading file to Synology:', error);
      throw error;
    }
  }

  /**
   * Create a sharing link
   * @param {string} path Path to the file or folder
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Sharing link info
   */
  async createSharingLink(path, options = {}) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const params = {
        api: 'SYNO.FileStation.Sharing',
        version: '3',
        method: 'create',
        path,
        _sid: this.sid,
        ...options
      };

      const response = await this.axios.get('/entry.cgi', { params });
      
      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error.code);
      }
    } catch (error) {
      console.error('Error creating sharing link on Synology:', error);
      throw error;
    }
  }
}

module.exports = SynologyAPI;