// Lenel S2 NetBox API Wrapper
const axios = require('axios');
const https = require('https');

/**
 * Lenel S2 NetBox API Wrapper
 * This wrapper is designed to interact with Lenel S2 NetBox security systems
 * Note: This system should only be accessed from the local network for security reasons
 */
class LenelS2NetBoxAPI {
  constructor(host, username, password, port = 443) {
    this.host = host;
    this.username = username;
    this.password = password;
    this.port = port;
    this.baseURL = `https://${host}:${port}/api`;
    this.token = null;
    
    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  }

  /**
   * Authenticate with Lenel S2 NetBox
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      const response = await this.axios.post('/auth/login', {
        username: this.username,
        password: this.password
      });
      
      if (response.data && response.data.token) {
        this.token = response.data.token;
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }
      
      return this.token;
    } catch (error) {
      console.error('Error authenticating with Lenel S2 NetBox:', error);
      throw error;
    }
  }

  /**
   * Get all access points
   * @returns {Promise<Array>} List of access points
   */
  async getAccessPoints() {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/access-points');
      return response.data;
    } catch (error) {
      console.error('Error fetching Lenel S2 NetBox access points:', error);
      throw error;
    }
  }

  /**
   * Get access point details
   * @param {string} accessPointId Access point ID
   * @returns {Promise<Object>} Access point details
   */
  async getAccessPointDetails(accessPointId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/access-points/${accessPointId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Lenel S2 NetBox access point details for ${accessPointId}:`, error);
      throw error;
    }
  }

  /**
   * Get all cardholders
   * @returns {Promise<Array>} List of cardholders
   */
  async getCardholders() {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/cardholders');
      return response.data;
    } catch (error) {
      console.error('Error fetching Lenel S2 NetBox cardholders:', error);
      throw error;
    }
  }

  /**
   * Get cardholder details
   * @param {string} cardholderId Cardholder ID
   * @returns {Promise<Object>} Cardholder details
   */
  async getCardholderDetails(cardholderId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/cardholders/${cardholderId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Lenel S2 NetBox cardholder details for ${cardholderId}:`, error);
      throw error;
    }
  }

  /**
   * Get all alarms
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of alarms
   */
  async getAlarms(params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/alarms', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Lenel S2 NetBox alarms:', error);
      throw error;
    }
  }

  /**
   * Acknowledge an alarm
   * @param {string} alarmId Alarm ID
   * @returns {Promise<Object>} Response data
   */
  async acknowledgeAlarm(alarmId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/alarms/${alarmId}/acknowledge`);
      return response.data;
    } catch (error) {
      console.error(`Error acknowledging Lenel S2 NetBox alarm ${alarmId}:`, error);
      throw error;
    }
  }

  /**
   * Get all events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of events
   */
  async getEvents(params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Lenel S2 NetBox events:', error);
      throw error;
    }
  }

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/system/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching Lenel S2 NetBox system status:', error);
      throw error;
    }
  }

  /**
   * Unlock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Response data
   */
  async unlockDoor(doorId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/doors/${doorId}/unlock`);
      return response.data;
    } catch (error) {
      console.error(`Error unlocking Lenel S2 NetBox door ${doorId}:`, error);
      throw error;
    }
  }

  /**
   * Lock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Response data
   */
  async lockDoor(doorId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/doors/${doorId}/lock`);
      return response.data;
    } catch (error) {
      console.error(`Error locking Lenel S2 NetBox door ${doorId}:`, error);
      throw error;
    }
  }
}

module.exports = LenelS2NetBoxAPI;