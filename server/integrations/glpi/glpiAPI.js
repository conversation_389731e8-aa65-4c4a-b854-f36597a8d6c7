// GLPI API Wrapper
const axios = require('axios');

/**
 * GLPI API Wrapper
 * Documentation: https://github.com/glpi-project/glpi/blob/master/apirest.md
 */
class GLPIAPI {
  constructor(url, appToken, userToken = null) {
    this.url = url;
    this.appToken = appToken;
    this.userToken = userToken;
    this.sessionToken = null;
    this.axios = axios.create({
      baseURL: url,
      headers: {
        'Content-Type': 'application/json',
        'App-Token': appToken
      }
    });
  }

  /**
   * Initialize the session with GLPI
   * @param {string} username - GLPI username
   * @param {string} password - GLPI password
   * @returns {Promise<string>} Session token
   */
  async initSessionByCredentials(username, password) {
    try {
      const response = await this.axios.get('/initSession', {
        auth: {
          username,
          password
        }
      });

      if (response.data && response.data.session_token) {
        this.sessionToken = response.data.session_token;
        this.axios.defaults.headers.common['Session-Token'] = this.sessionToken;
        return this.sessionToken;
      } else {
        throw new Error('Failed to initialize session: No session token received');
      }
    } catch (error) {
      console.error('Error initializing session by credentials:', error);
      throw error;
    }
  }

  /**
   * Initialize the session with GLPI using user token
   * @returns {Promise<string>} Session token
   */
  async initSessionByUserToken() {
    try {
      if (!this.userToken) {
        throw new Error('User token is required');
      }

      const response = await this.axios.get('/initSession', {
        headers: {
          'Authorization': `user_token ${this.userToken}`
        }
      });

      if (response.data && response.data.session_token) {
        this.sessionToken = response.data.session_token;
        this.axios.defaults.headers.common['Session-Token'] = this.sessionToken;
        return this.sessionToken;
      } else {
        throw new Error('Failed to initialize session: No session token received');
      }
    } catch (error) {
      console.error('Error initializing session by user token:', error);
      throw error;
    }
  }

  /**
   * Kill the current session
   * @returns {Promise<boolean>} Success status
   */
  async killSession() {
    try {
      if (!this.sessionToken) {
        return true;
      }

      await this.axios.get('/killSession');
      this.sessionToken = null;
      delete this.axios.defaults.headers.common['Session-Token'];
      return true;
    } catch (error) {
      console.error('Error killing session:', error);
      throw error;
    }
  }

  /**
   * Get GLPI profile information
   * @returns {Promise<Object>} Profile information
   */
  async getMyProfiles() {
    try {
      this.checkSession();
      const response = await this.axios.get('/getMyProfiles');
      return response.data;
    } catch (error) {
      console.error('Error getting profiles:', error);
      throw error;
    }
  }

  /**
   * Get all assets
   * @param {Object} options - Query options
   * @returns {Promise<Array>} List of assets
   */
  async getAssets(options = {}) {
    try {
      this.checkSession();
      const response = await this.axios.get('/Computer', { params: options });
      return response.data;
    } catch (error) {
      console.error('Error getting assets:', error);
      throw error;
    }
  }

  /**
   * Get asset by ID
   * @param {number} id - Asset ID
   * @returns {Promise<Object>} Asset details
   */
  async getAsset(id) {
    try {
      this.checkSession();
      const response = await this.axios.get(`/Computer/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error getting asset with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Search assets
   * @param {Object} criteria - Search criteria
   * @returns {Promise<Array>} Search results
   */
  async searchAssets(criteria) {
    try {
      this.checkSession();
      const response = await this.axios.get('/search/Computer', { params: criteria });
      return response.data;
    } catch (error) {
      console.error('Error searching assets:', error);
      throw error;
    }
  }

  /**
   * Get all asset types
   * @returns {Promise<Array>} List of asset types
   */
  async getAssetTypes() {
    try {
      this.checkSession();
      const response = await this.axios.get('/ComputerType');
      return response.data;
    } catch (error) {
      console.error('Error getting asset types:', error);
      throw error;
    }
  }

  /**
   * Get all asset models
   * @returns {Promise<Array>} List of asset models
   */
  async getAssetModels() {
    try {
      this.checkSession();
      const response = await this.axios.get('/ComputerModel');
      return response.data;
    } catch (error) {
      console.error('Error getting asset models:', error);
      throw error;
    }
  }

  /**
   * Get all asset manufacturers
   * @returns {Promise<Array>} List of asset manufacturers
   */
  async getAssetManufacturers() {
    try {
      this.checkSession();
      const response = await this.axios.get('/Manufacturer');
      return response.data;
    } catch (error) {
      console.error('Error getting asset manufacturers:', error);
      throw error;
    }
  }

  /**
   * Create a new asset
   * @param {Object} assetData - Asset data
   * @returns {Promise<Object>} Created asset
   */
  async createAsset(assetData) {
    try {
      this.checkSession();
      const response = await this.axios.post('/Computer', assetData);
      return response.data;
    } catch (error) {
      console.error('Error creating asset:', error);
      throw error;
    }
  }

  /**
   * Update an asset
   * @param {number} id - Asset ID
   * @param {Object} assetData - Asset data
   * @returns {Promise<Object>} Updated asset
   */
  async updateAsset(id, assetData) {
    try {
      this.checkSession();
      const response = await this.axios.put(`/Computer/${id}`, assetData);
      return response.data;
    } catch (error) {
      console.error(`Error updating asset with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an asset
   * @param {number} id - Asset ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteAsset(id) {
    try {
      this.checkSession();
      await this.axios.delete(`/Computer/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting asset with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Check if session is active
   * @throws {Error} If session is not active
   */
  checkSession() {
    if (!this.sessionToken) {
      throw new Error('No active session. Please initialize session first.');
    }
  }
}

module.exports = GLPIAPI;