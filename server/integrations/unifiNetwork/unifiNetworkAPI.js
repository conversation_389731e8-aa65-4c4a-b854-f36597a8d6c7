// UniFi Network API Wrapper
const axios = require('axios');
const https = require('https');

/**
 * UniFi Network API Wrapper
 * Documentation: https://ubntwiki.com/products/software/unifi-controller/api
 */
class UnifiNetworkAPI {
  constructor(host, username, password, port = 443, site = 'default') {
    this.host = host;
    this.username = username;
    this.password = password;
    this.port = port;
    this.site = site;
    this.baseURL = `https://${host}:${port}`;
    this.token = null;
    this.cookies = null;
    
    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  }

  /**
   * Authenticate with UniFi Network Controller
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      const response = await this.axios.post('/api/login', {
        username: this.username,
        password: this.password
      });
      
      // Store cookies for future requests
      if (response.headers['set-cookie']) {
        this.cookies = response.headers['set-cookie'];
        this.axios.defaults.headers.common['Cookie'] = this.cookies.join('; ');
      }
      
      return this.cookies;
    } catch (error) {
      console.error('Error authenticating with UniFi Network Controller:', error);
      throw error;
    }
  }

  /**
   * Get all devices
   * @returns {Promise<Array>} List of devices
   */
  async getDevices() {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/api/s/${this.site}/stat/device`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching UniFi Network devices:', error);
      throw error;
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID (MAC address)
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/api/s/${this.site}/stat/device/${deviceId}`);
      return response.data.data[0];
    } catch (error) {
      console.error(`Error fetching UniFi Network device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get all clients
   * @returns {Promise<Array>} List of clients
   */
  async getClients() {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/api/s/${this.site}/stat/sta`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching UniFi Network clients:', error);
      throw error;
    }
  }

  /**
   * Get client details
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Client details
   */
  async getClientDetails(clientId) {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/api/s/${this.site}/stat/user/${clientId}`);
      return response.data.data[0];
    } catch (error) {
      console.error(`Error fetching UniFi Network client details for ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Block a client
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Response data
   */
  async blockClient(clientId) {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/api/s/${this.site}/cmd/stamgr`, {
        cmd: 'block-sta',
        mac: clientId
      });
      return response.data;
    } catch (error) {
      console.error(`Error blocking UniFi Network client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Unblock a client
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Response data
   */
  async unblockClient(clientId) {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/api/s/${this.site}/cmd/stamgr`, {
        cmd: 'unblock-sta',
        mac: clientId
      });
      return response.data;
    } catch (error) {
      console.error(`Error unblocking UniFi Network client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Get network statistics
   * @returns {Promise<Object>} Network statistics
   */
  async getNetworkStats() {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/api/s/${this.site}/stat/health`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching UniFi Network statistics:', error);
      throw error;
    }
  }

  /**
   * Get site information
   * @returns {Promise<Object>} Site information
   */
  async getSiteInfo() {
    try {
      if (!this.cookies) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/api/s/${this.site}/stat/sysinfo`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching UniFi Network site information:', error);
      throw error;
    }
  }
}

module.exports = UnifiNetworkAPI;