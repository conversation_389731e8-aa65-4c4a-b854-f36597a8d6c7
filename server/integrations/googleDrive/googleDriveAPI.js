// Google Drive API Wrapper
const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');

/**
 * Google Drive API Wrapper
 * Documentation: https://developers.google.com/drive/api/v3/reference
 */
class GoogleDriveAPI {
  constructor(clientId, clientSecret, redirectUri, tokenPath) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.tokenPath = tokenPath;
    this.scopes = [
      'https://www.googleapis.com/auth/drive.readonly',
      'https://www.googleapis.com/auth/drive.metadata.readonly'
    ];
    this.auth = null;
    this.drive = null;
  }

  /**
   * Initialize the Google Drive API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      this.auth = new google.auth.OAuth2(
        this.clientId,
        this.clientSecret,
        this.redirectUri
      );

      // Check if we have a stored token
      if (fs.existsSync(this.tokenPath)) {
        const token = JSON.parse(fs.readFileSync(this.tokenPath, 'utf8'));
        this.auth.setCredentials(token);
      }

      this.drive = google.drive({ version: 'v3', auth: this.auth });
    } catch (error) {
      console.error('Error initializing Google Drive API:', error);
      throw error;
    }
  }

  /**
   * Generate authentication URL for OAuth2 flow
   * @returns {string} Authentication URL
   */
  getAuthUrl() {
    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: this.scopes,
      prompt: 'consent'
    });
  }

  /**
   * Get access token from authorization code
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Token object
   */
  async getToken(code) {
    try {
      const { tokens } = await this.auth.getToken(code);
      this.auth.setCredentials(tokens);
      
      // Save token to file for future use
      fs.writeFileSync(this.tokenPath, JSON.stringify(tokens));
      
      return tokens;
    } catch (error) {
      console.error('Error getting token:', error);
      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    return this.auth && this.auth.credentials && this.auth.credentials.access_token;
  }

  /**
   * List files in Google Drive
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of files
   */
  async listFiles(options = {}) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        pageSize: 30,
        fields: 'nextPageToken, files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime, size, parents)'
      };

      const response = await this.drive.files.list({
        ...defaultOptions,
        ...options
      });

      return response.data.files;
    } catch (error) {
      console.error('Error listing files:', error);
      throw error;
    }
  }

  /**
   * Get file metadata
   * @param {string} fileId File ID
   * @returns {Promise<Object>} File metadata
   */
  async getFile(fileId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const response = await this.drive.files.get({
        fileId,
        fields: 'id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime, size, parents'
      });

      return response.data;
    } catch (error) {
      console.error('Error getting file:', error);
      throw error;
    }
  }

  /**
   * Search for files in Google Drive
   * @param {string} query Search query
   * @param {Object} options Additional options
   * @returns {Promise<Array>} List of files
   */
  async searchFiles(query, options = {}) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        pageSize: 30,
        fields: 'nextPageToken, files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime, size, parents)',
        q: query
      };

      const response = await this.drive.files.list({
        ...defaultOptions,
        ...options
      });

      return response.data.files;
    } catch (error) {
      console.error('Error searching files:', error);
      throw error;
    }
  }

  /**
   * Get a shareable link for a file
   * @param {string} fileId File ID
   * @returns {Promise<string>} Shareable link
   */
  async getShareableLink(fileId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const file = await this.getFile(fileId);
      return file.webViewLink;
    } catch (error) {
      console.error('Error getting shareable link:', error);
      throw error;
    }
  }

  /**
   * Get an embedded viewer URL for Google Docs, Sheets, Slides
   * @param {string} fileId File ID
   * @returns {string} Embedded viewer URL
   */
  getEmbeddedViewerUrl(fileId) {
    return `https://docs.google.com/viewer?srcid=${fileId}&pid=explorer&efh=false&a=v&chrome=false&embedded=true`;
  }
}

module.exports = GoogleDriveAPI;