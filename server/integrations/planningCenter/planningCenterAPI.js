// Planning Center API Wrapper
const axios = require('axios');

/**
 * Planning Center API Wrapper
 * Documentation: https://developer.planning.center/docs/#/
 */
class PlanningCenterAPI {
  constructor(clientId, clientSecret) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.baseURL = 'https://api.planningcenteronline.com';
    this.axios = axios.create({
      baseURL: this.baseURL,
      auth: {
        username: this.clientId,
        password: this.clientSecret
      }
    });
  }

  /**
   * Get all available applications
   * @returns {Promise<Array>} List of applications
   */
  async getApplications() {
    try {
      const response = await this.axios.get('/');
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center applications:', error);
      throw error;
    }
  }

  /**
   * Get calendar events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of events
   */
  async getEvents(params = {}) {
    try {
      const response = await this.axios.get('/calendar/v2/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center events:', error);
      throw error;
    }
  }

  /**
   * Get people
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of people
   */
  async getPeople(params = {}) {
    try {
      const response = await this.axios.get('/people/v2/people', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center people:', error);
      throw error;
    }
  }

  /**
   * Get resources
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of resources
   */
  async getResources(params = {}) {
    try {
      const response = await this.axios.get('/calendar/v2/resources', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center resources:', error);
      throw error;
    }
  }
}

module.exports = PlanningCenterAPI;