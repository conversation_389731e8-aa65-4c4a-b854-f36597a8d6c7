// Dreo API Wrapper
const axios = require('axios');

/**
 * Dreo API Wrapper
 * Documentation: https://api.dreo.com/docs
 */
class DreoAPI {
  constructor(username, password) {
    this.username = username;
    this.password = password;
    this.baseURL = 'https://api.dreo.com';
    this.token = null;
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Authenticate with Dreo API
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      const response = await this.axios.post('/auth/login', {
        username: this.username,
        password: this.password
      });
      this.token = response.data.token;
      // Set the token for all future requests
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      return this.token;
    } catch (error) {
      console.error('Error authenticating with Dreo API:', error);
      throw error;
    }
  }

  /**
   * Get all devices
   * @returns {Promise<Array>} List of devices
   */
  async getDevices() {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      const response = await this.axios.get('/devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching Dreo devices:', error);
      throw error;
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      const response = await this.axios.get(`/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Dreo device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device power state
   * @param {string} deviceId Device ID
   * @param {boolean} power Power state (true = on, false = off)
   * @returns {Promise<Object>} Operation result
   */
  async setPower(deviceId, power) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      const response = await this.axios.post(`/devices/${deviceId}/power`, {
        power: power
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting Dreo device power for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device temperature
   * @param {string} deviceId Device ID
   * @param {number} temperature Temperature in Celsius
   * @returns {Promise<Object>} Operation result
   */
  async setTemperature(deviceId, temperature) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      const response = await this.axios.post(`/devices/${deviceId}/temperature`, {
        temperature: temperature
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting Dreo device temperature for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device fan speed
   * @param {string} deviceId Device ID
   * @param {number} speed Fan speed (1-5)
   * @returns {Promise<Object>} Operation result
   */
  async setFanSpeed(deviceId, speed) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      const response = await this.axios.post(`/devices/${deviceId}/fan-speed`, {
        speed: speed
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting Dreo device fan speed for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device mode
   * @param {string} deviceId Device ID
   * @param {string} mode Device mode (cool, fan, dry)
   * @returns {Promise<Object>} Operation result
   */
  async setMode(deviceId, mode) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      const response = await this.axios.post(`/devices/${deviceId}/mode`, {
        mode: mode
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting Dreo device mode for ${deviceId}:`, error);
      throw error;
    }
  }
}

module.exports = DreoAPI;