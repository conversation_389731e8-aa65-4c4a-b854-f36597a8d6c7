// Canvas LMS API Wrapper
const axios = require('axios');

/**
 * Canvas LMS API Wrapper
 * Documentation: https://canvas.instructure.com/doc/api/
 */
class CanvasAPI {
  constructor(domain, apiKey) {
    this.domain = domain;
    this.apiKey = apiKey;
    this.baseURL = `https://${domain}/api/v1`;
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
  }

  /**
   * Get courses
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of courses
   */
  async getCourses(params = {}) {
    try {
      const response = await this.axios.get('/courses', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canvas courses:', error);
      throw error;
    }
  }

  /**
   * Get course details
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Course details
   */
  async getCourse(courseId, params = {}) {
    try {
      const response = await this.axios.get(`/courses/${courseId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId}:`, error);
      throw error;
    }
  }

  /**
   * Get course modules
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of modules
   */
  async getCourseModules(courseId, params = {}) {
    try {
      const response = await this.axios.get(`/courses/${courseId}/modules`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId} modules:`, error);
      throw error;
    }
  }

  /**
   * Get course assignments
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of assignments
   */
  async getCourseAssignments(courseId, params = {}) {
    try {
      const response = await this.axios.get(`/courses/${courseId}/assignments`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId} assignments:`, error);
      throw error;
    }
  }

  /**
   * Get course files
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of files
   */
  async getCourseFiles(courseId, params = {}) {
    try {
      const response = await this.axios.get(`/courses/${courseId}/files`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId} files:`, error);
      throw error;
    }
  }

  /**
   * Get file details
   * @param {string} fileId File ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} File details
   */
  async getFile(fileId, params = {}) {
    try {
      const response = await this.axios.get(`/files/${fileId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Get users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  async getUsers(params = {}) {
    try {
      const response = await this.axios.get('/accounts/self/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canvas users:', error);
      throw error;
    }
  }

  /**
   * Get user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  async getUser(userId, params = {}) {
    try {
      const response = await this.axios.get(`/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas user ${userId}:`, error);
      throw error;
    }
  }
}

module.exports = CanvasAPI;