// UniFi Protect API Wrapper
const axios = require('axios');
const https = require('https');

/**
 * UniFi Protect API Wrapper
 * Documentation: https://github.com/hjdhjd/unifi-protect
 */
class UnifiProtectAPI {
  constructor(host, username, password, port = 443) {
    this.host = host;
    this.username = username;
    this.password = password;
    this.port = port;
    this.baseURL = `https://${host}:${port}/api`;
    this.token = null;
    this.cookies = null;
    
    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  }

  /**
   * Authenticate with UniFi Protect
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      const response = await this.axios.post('/auth/login', {
        username: this.username,
        password: this.password
      });
      
      // Store cookies for future requests
      if (response.headers['set-cookie']) {
        this.cookies = response.headers['set-cookie'];
        this.axios.defaults.headers.common['Cookie'] = this.cookies.join('; ');
      }
      
      // Store token if available
      if (response.data && response.data.token) {
        this.token = response.data.token;
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }
      
      return this.token;
    } catch (error) {
      console.error('Error authenticating with UniFi Protect:', error);
      throw error;
    }
  }

  /**
   * Get all cameras
   * @returns {Promise<Array>} List of cameras
   */
  async getCameras() {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/cameras');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect cameras:', error);
      throw error;
    }
  }

  /**
   * Get camera details
   * @param {string} cameraId Camera ID
   * @returns {Promise<Object>} Camera details
   */
  async getCameraDetails(cameraId) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/cameras/${cameraId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera details for ${cameraId}:`, error);
      throw error;
    }
  }

  /**
   * Get camera snapshot
   * @param {string} cameraId Camera ID
   * @returns {Promise<Buffer>} Camera snapshot image
   */
  async getCameraSnapshot(cameraId) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/cameras/${cameraId}/snapshot`, {
        responseType: 'arraybuffer'
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera snapshot for ${cameraId}:`, error);
      throw error;
    }
  }

  /**
   * Get all events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of events
   */
  async getEvents(params = {}) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect events:', error);
      throw error;
    }
  }

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/system');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect system status:', error);
      throw error;
    }
  }
}

module.exports = UnifiProtectAPI;