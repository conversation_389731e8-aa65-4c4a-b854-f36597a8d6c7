// Mosyle Business API Wrapper
const axios = require('axios');

/**
 * Mosyle Business API Wrapper
 * Documentation: https://business.mosyle.com/api
 */
class MosyleBusinessAPI {
  constructor(apiKey, domain) {
    this.apiKey = apiKey;
    this.domain = domain;
    this.baseURL = `https://${domain}.mosyle.com/api/v2`;
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
  }

  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  async getDevices(params = {}) {
    try {
      const response = await this.axios.get('/devices', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle Business devices:', error);
      throw error;
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      const response = await this.axios.get(`/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Mosyle Business device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get all users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  async getUsers(params = {}) {
    try {
      const response = await this.axios.get('/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle Business users:', error);
      throw error;
    }
  }

  /**
   * Get all groups
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of groups
   */
  async getGroups(params = {}) {
    try {
      const response = await this.axios.get('/groups', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle Business groups:', error);
      throw error;
    }
  }
}

module.exports = MosyleBusinessAPI;