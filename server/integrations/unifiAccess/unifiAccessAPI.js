// UniFi Access API Wrapper
const axios = require('axios');
const https = require('https');

/**
 * UniFi Access API Wrapper
 * Documentation: https://github.com/hjdhjd/unifi-access
 */
class UnifiAccessAPI {
  constructor(host, username, password, port = 443) {
    this.host = host;
    this.username = username;
    this.password = password;
    this.port = port;
    this.baseURL = `https://${host}:${port}/api`;
    this.token = null;
    this.cookies = null;
    
    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  }

  /**
   * Authenticate with UniFi Access
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      const response = await this.axios.post('/auth/login', {
        username: this.username,
        password: this.password
      });
      
      // Store cookies for future requests
      if (response.headers['set-cookie']) {
        this.cookies = response.headers['set-cookie'];
        this.axios.defaults.headers.common['Cookie'] = this.cookies.join('; ');
      }
      
      // Store token if available
      if (response.data && response.data.token) {
        this.token = response.data.token;
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }
      
      return this.token;
    } catch (error) {
      console.error('Error authenticating with UniFi Access:', error);
      throw error;
    }
  }

  /**
   * Get all doors
   * @returns {Promise<Array>} List of doors
   */
  async getDoors() {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/doors');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access doors:', error);
      throw error;
    }
  }

  /**
   * Get door details
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Door details
   */
  async getDoorDetails(doorId) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get(`/doors/${doorId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access door details for ${doorId}:`, error);
      throw error;
    }
  }

  /**
   * Unlock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Operation result
   */
  async unlockDoor(doorId) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/doors/${doorId}/unlock`);
      return response.data;
    } catch (error) {
      console.error(`Error unlocking UniFi Access door ${doorId}:`, error);
      throw error;
    }
  }

  /**
   * Lock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Operation result
   */
  async lockDoor(doorId) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.post(`/doors/${doorId}/lock`);
      return response.data;
    } catch (error) {
      console.error(`Error locking UniFi Access door ${doorId}:`, error);
      throw error;
    }
  }

  /**
   * Get all access points
   * @returns {Promise<Array>} List of access points
   */
  async getAccessPoints() {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/access-points');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access access points:', error);
      throw error;
    }
  }

  /**
   * Get all users
   * @returns {Promise<Array>} List of users
   */
  async getUsers() {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/users');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access users:', error);
      throw error;
    }
  }

  /**
   * Get access events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access events
   */
  async getEvents(params = {}) {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access events:', error);
      throw error;
    }
  }

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      if (!this.cookies && !this.token) {
        await this.authenticate();
      }
      
      const response = await this.axios.get('/system');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access system status:', error);
      throw error;
    }
  }
}

module.exports = UnifiAccessAPI;