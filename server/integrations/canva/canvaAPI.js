// Canva API Wrapper
const axios = require('axios');

/**
 * Canva API Wrapper
 * Documentation: https://www.canva.com/developers/
 */
class CanvaAPI {
  constructor(domain, apiKey) {
    this.domain = domain;
    this.apiKey = apiKey;
    this.baseURL = `https://api.${domain}/v1`;
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
  }

  /**
   * Get designs
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of designs
   */
  async getDesigns(params = {}) {
    try {
      const response = await this.axios.get('/designs', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva designs:', error);
      throw error;
    }
  }

  /**
   * Get design details
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Design details
   */
  async getDesign(designId, params = {}) {
    try {
      const response = await this.axios.get(`/designs/${designId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId}:`, error);
      throw error;
    }
  }

  /**
   * Get templates
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of templates
   */
  async getTemplates(params = {}) {
    try {
      const response = await this.axios.get('/templates', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva templates:', error);
      throw error;
    }
  }

  /**
   * Get design assets
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of assets
   */
  async getDesignAssets(designId, params = {}) {
    try {
      const response = await this.axios.get(`/designs/${designId}/assets`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId} assets:`, error);
      throw error;
    }
  }

  /**
   * Get design files
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of files
   */
  async getDesignFiles(designId, params = {}) {
    try {
      const response = await this.axios.get(`/designs/${designId}/files`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId} files:`, error);
      throw error;
    }
  }

  /**
   * Get file details
   * @param {string} fileId File ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} File details
   */
  async getFile(fileId, params = {}) {
    try {
      const response = await this.axios.get(`/files/${fileId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Get users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  async getUsers(params = {}) {
    try {
      const response = await this.axios.get('/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva users:', error);
      throw error;
    }
  }

  /**
   * Get user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  async getUser(userId, params = {}) {
    try {
      const response = await this.axios.get(`/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva user ${userId}:`, error);
      throw error;
    }
  }
}

module.exports = CanvaAPI;