# CSF Portal Production Environment Configuration

This document outlines the configuration and setup for the CSF Portal production environment.

## Table of Contents

- [Server Requirements](#server-requirements)
- [Installation](#installation)
- [Environment Configuration](#environment-configuration)
- [Database Setup](#database-setup)
- [Web Server Configuration](#web-server-configuration)
- [SSL/TLS Setup](#ssltls-setup)
- [Security Considerations](#security-considerations)
- [Third-Party Service Configuration](#third-party-service-configuration)

## Server Requirements

### Hardware Requirements

- **CPU**: 2+ cores
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 20GB minimum (SSD recommended)
- **Network**: Reliable internet connection with static IP

### Software Requirements

- **Operating System**: Ubuntu 20.04 LTS or later
- **Node.js**: v16.x
- **MongoDB**: v4.4 or later
- **Nginx**: v1.18 or later (as reverse proxy)
- **Let's Encrypt**: For SSL certificates

## Installation

### 1. Update System

```bash
sudo apt update
sudo apt upgrade -y
```

### 2. Install Node.js

```bash
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt install -y nodejs
```

### 3. Install MongoDB

```bash
wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list
sudo apt update
sudo apt install -y mongodb-org
sudo systemctl enable mongod
sudo systemctl start mongod
```

### 4. Install Nginx

```bash
sudo apt install -y nginx
sudo systemctl enable nginx
sudo systemctl start nginx
```

### 5. Clone and Setup Application

```bash
# Create application directory
sudo mkdir -p /var/www/csfportal
sudo chown -R $USER:$USER /var/www/csfportal

# Clone repository
git clone https://github.com/your-organization/csfportal.git /var/www/csfportal
cd /var/www/csfportal

# Install dependencies
npm install
npm run client-install
npm run build --prefix client
```

## Environment Configuration

Create a `.env` file in the application root with production settings:

```
NODE_ENV=production
PORT=5000
MONGO_URI=mongodb://localhost:27017/csfportal
SESSION_SECRET=your_strong_random_session_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=https://portal.ukcsf.org/api/auth/google/callback
```

## Database Setup

### 1. Create MongoDB User

```bash
mongosh
```

```javascript
use admin
db.createUser({
  user: "admin",
  pwd: "secure_password",
  roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
})
exit
```

### 2. Enable MongoDB Authentication

Edit the MongoDB configuration file:

```bash
sudo nano /etc/mongod.conf
```

Add/modify the security section:

```yaml
security:
  authorization: enabled
```

Restart MongoDB:

```bash
sudo systemctl restart mongod
```

### 3. Create Application Database and User

```bash
mongosh -u admin -p secure_password --authenticationDatabase admin
```

```javascript
use csfportal
db.createUser({
  user: "csfportal",
  pwd: "app_specific_password",
  roles: [ { role: "readWrite", db: "csfportal" } ]
})
exit
```

Update the `.env` file with the new connection string:

```
MONGO_URI=*******************************************************************
```

## Web Server Configuration

### 1. Create Systemd Service

Create a service file:

```bash
sudo nano /etc/systemd/system/csfportal.service
```

Add the following content:

```
[Unit]
Description=CSF Portal
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/csfportal
ExecStart=/usr/bin/npm start
Restart=on-failure
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl enable csfportal
sudo systemctl start csfportal
```

### 2. Configure Nginx as Reverse Proxy

Create a new Nginx site configuration:

```bash
sudo nano /etc/nginx/sites-available/csfportal
```

Add the following content:

```
server {
    listen 80;
    server_name portal.ukcsf.org;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/csfportal /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL/TLS Setup

### 1. Install Certbot

```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 2. Obtain SSL Certificate

```bash
sudo certbot --nginx -d portal.ukcsf.org
```

Follow the prompts to complete the certificate setup.

## Security Considerations

### 1. Firewall Configuration

```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Secure MongoDB

Ensure MongoDB is not exposed to the internet:

```bash
sudo nano /etc/mongod.conf
```

Make sure the bindIp is set to localhost:

```yaml
net:
  port: 27017
  bindIp: 127.0.0.1
```

### 3. Regular Updates

Set up automatic security updates:

```bash
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## Third-Party Service Configuration

### 1. Google OAuth

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Create an OAuth 2.0 Client ID
5. Add authorized redirect URIs:
   - https://portal.ukcsf.org/api/auth/google/callback
6. Update the `.env` file with the client ID and secret

### 2. Planning Center API

1. Register a new application in Planning Center
2. Obtain API credentials
3. Add the credentials to the `.env` file:
   ```
   PLANNING_CENTER_APP_ID=your_app_id
   PLANNING_CENTER_SECRET=your_app_secret
   ```

### 3. Synology API

1. Enable the Synology API in your Synology NAS
2. Create an API user with appropriate permissions
3. Add the credentials to the `.env` file:
   ```
   SYNOLOGY_HOST=your_synology_host
   SYNOLOGY_USER=your_api_user
   SYNOLOGY_PASSWORD=your_api_password
   ```

### 4. Canva API

1. Generate an API key in Canva
2. Add the key to the `.env` file:
   ```
   CANVA_API_URL=your_canva_instance_url
   CANVA_API_KEY=your_api_key
   ```

### 5. GLPI API

1. Enable the REST API in GLPI
2. Create an API client
3. Add the credentials to the `.env` file:
   ```
   GLPI_API_URL=your_glpi_url
   GLPI_APP_TOKEN=your_app_token
   GLPI_USER_TOKEN=your_user_token
   ```
