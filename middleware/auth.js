const User = require('../models/User');

/**
 * Middleware to check if user is authenticated
 */
exports.isAuthenticated = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ msg: 'Not authenticated. Please log in.' });
};

/**
 * Middleware to check if user has required roles
 * @param {Array} roles - Array of required roles
 */
exports.hasRoles = (roles) => {
  return async (req, res, next) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ msg: 'Not authenticated. Please log in.' });
    }

    try {
      // Get user with fresh data from database
      const user = await User.findById(req.user.id);
      
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({ msg: 'User account is inactive' });
      }

      // Check if user has admin role (admins can access everything)
      if (user.roles.includes('admin')) {
        return next();
      }

      // Check if user has at least one of the required roles
      const hasRequiredRole = user.roles.some(role => roles.includes(role));
      
      if (!hasRequiredRole) {
        return res.status(403).json({ msg: 'Access denied. Insufficient permissions.' });
      }

      // Update user's last login time
      user.lastLogin = Date.now();
      await user.save();

      next();
    } catch (err) {
      console.error('Error in role authorization:', err);
      res.status(500).json({ msg: 'Server error during authorization' });
    }
  };
};

/**
 * Middleware to check if user has specific permission
 * @param {String} permission - Required permission
 */
exports.hasPermission = (permission) => {
  return async (req, res, next) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ msg: 'Not authenticated. Please log in.' });
    }

    try {
      // Get user with fresh data from database
      const user = await User.findById(req.user.id).populate('roles');
      
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({ msg: 'User account is inactive' });
      }

      // Check if user has admin role (admins have all permissions)
      if (user.roles.includes('admin')) {
        return next();
      }

      // Get all roles for the user
      const Role = require('../models/Role');
      const userRoles = await Role.find({ name: { $in: user.roles } });
      
      // Check if any of the user's roles have the required permission
      // or have the wildcard permission '*'
      const hasPermission = userRoles.some(role => 
        role.permissions.includes(permission) || role.permissions.includes('*')
      );
      
      if (!hasPermission) {
        return res.status(403).json({ msg: 'Access denied. Insufficient permissions.' });
      }

      next();
    } catch (err) {
      console.error('Error in permission authorization:', err);
      res.status(500).json({ msg: 'Server error during authorization' });
    }
  };
};