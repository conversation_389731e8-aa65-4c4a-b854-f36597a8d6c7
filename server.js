require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const passport = require('passport');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');

// Initialize Express app
const app = express();

// Security middleware
app.use(helmet());
app.use(cors());

// Body parser middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Configure session storage
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'keyboard cat',
    resave: false,
    saveUninitialized: false,
    store: MongoStore.create({
      mongoUrl: process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal',
      collectionName: 'sessions'
    }),
    cookie: {
      maxAge: 1000 * 60 * 60 * 24, // 1 day
      secure: process.env.NODE_ENV === 'production'
    }
  })
);

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Passport config
require('./config/passport')(passport);

// Connect to MongoDB
mongoose
  .connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
    useNewUrlParser: true,
    useUnifiedTopology: true
  })
  .then(() => console.log('MongoDB Connected'))
  .catch(err => console.log(err));

// Define Routes
app.use('/api/auth', require('./routes/api/auth'));
app.use('/api/users', require('./routes/api/users'));
app.use('/api/roles', require('./routes/api/roles'));
app.use('/api/shortcuts', require('./routes/api/shortcuts'));
app.use('/api/google', require('./routes/api/google'));
app.use('/api/google-drive', require('./routes/api/googleDrive'));
app.use('/api/planning-center', require('./routes/api/planningCenter'));
app.use('/api/synology', require('./routes/api/synology'));
app.use('/api/canva', require('./routes/api/canva'));
app.use('/api/glpi', require('./routes/api/glpi'));
app.use('/api/mosyle-business', require('./routes/api/mosyleBusiness'));
app.use('/api/dreo', require('./routes/api/dreo'));
app.use('/api/unifi-protect', require('./routes/api/unifiProtect'));
app.use('/api/unifi-access', require('./routes/api/unifiAccess'));
app.use('/api/unifi-network', require('./routes/api/unifiNetwork'));
app.use('/api/lenel-s2-netbox', require('./routes/api/lenelS2NetBox'));
app.use('/api/help/entries', require('./routes/api/helpEntries'));
app.use('/api/help/categories', require('./routes/api/helpCategories'));
app.use('/api/asset-requests', require('./routes/api/assetRequests'));
app.use('/api/asset-issues', require('./routes/api/assetIssues'));
app.use('/api/asset-reports', require('./routes/api/assetReports'));

// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  // Set static folder
  app.use(express.static('client/build'));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, 'client', 'build', 'index.html'));
  });
}

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => console.log(`Server started on port ${PORT}`));
