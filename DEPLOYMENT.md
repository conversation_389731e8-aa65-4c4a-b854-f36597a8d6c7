# CSF Portal Deployment Process

This document outlines the deployment process for the CSF Portal application.

## Table of Contents

- [Deployment Environments](#deployment-environments)
- [Prerequisites](#prerequisites)
- [Deployment Pipeline](#deployment-pipeline)
- [Manual Deployment](#manual-deployment)
- [Rollback Procedures](#rollback-procedures)
- [Post-Deployment Verification](#post-deployment-verification)
- [Deployment Checklist](#deployment-checklist)

## Deployment Environments

The CSF Portal has three deployment environments:

1. **Development** - For active development and testing
2. **Staging** - For pre-production testing and validation
3. **Production** - The live environment used by end users

| Environment | URL | Purpose | Deployment Frequency |
|-------------|-----|---------|---------------------|
| Development | https://dev.portal.ukcsf.org | Development and testing | Continuous |
| Staging | https://staging.portal.ukcsf.org | Pre-production validation | On feature completion |
| Production | https://portal.ukcsf.org | Live environment | Scheduled releases |

## Prerequisites

Before deploying the CSF Portal, ensure the following prerequisites are met:

1. Access to the deployment environments
2. Required environment variables configured
3. Database migrations prepared (if applicable)
4. All tests passing in the CI/CD pipeline
5. Deployment approval (for production)

## Deployment Pipeline

The CSF Portal uses a CI/CD pipeline implemented with GitHub Actions. The pipeline is defined in `.github/workflows/ci-cd.yml`.

### Pipeline Stages

1. **Build** - Compile and build the application
2. **Test** - Run automated tests
3. **Deploy** - Deploy to the appropriate environment

### Deployment Triggers

| Environment | Trigger |
|-------------|---------|
| Development | Push to `develop` branch |
| Staging | Push to `staging` branch or manual trigger |
| Production | Push to `main` branch or manual trigger |

### Pipeline Configuration

The CI/CD pipeline is configured in `.github/workflows/ci-cd.yml`:

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16.x'
      - name: Install dependencies
        run: |
          npm ci
          npm run client-install
      - name: Build client
        run: npm run build --prefix client
      - name: Run tests
        run: npm test
      - name: Upload build artifacts
        uses: actions/upload-artifact@v2
        with:
          name: build-artifacts
          path: |
            client/build
            server.js
            package.json
            config/
            middleware/
            models/
            routes/

  deploy-development:
    needs: build
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    environment: development
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build-artifacts
      - name: Deploy to development
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USERNAME }}
          key: ${{ secrets.DEV_SSH_KEY }}
          script: |
            cd /var/www/csfportal-dev
            git pull origin develop
            npm ci --production
            npm run build --prefix client
            pm2 restart csfportal-dev

  deploy-staging:
    needs: build
    if: github.ref == 'refs/heads/staging' || github.event.inputs.environment == 'staging'
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build-artifacts
      - name: Deploy to staging
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /var/www/csfportal-staging
            git pull origin staging
            npm ci --production
            npm run build --prefix client
            pm2 restart csfportal-staging

  deploy-production:
    needs: build
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'production'
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build-artifacts
      - name: Deploy to production
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /var/www/csfportal
            git pull origin main
            npm ci --production
            npm run build --prefix client
            pm2 restart csfportal
```

## Manual Deployment

In case the automated pipeline fails or for special deployment scenarios, follow these steps for manual deployment:

### Development Environment

```bash
# SSH into the development server
ssh <EMAIL>

# Navigate to the application directory
cd /var/www/csfportal-dev

# Pull the latest changes
git pull origin develop

# Install dependencies
npm ci --production
npm run client-install

# Build the client
npm run build --prefix client

# Restart the application
pm2 restart csfportal-dev
```

### Staging Environment

```bash
# SSH into the staging server
ssh <EMAIL>

# Navigate to the application directory
cd /var/www/csfportal-staging

# Pull the latest changes
git pull origin staging

# Install dependencies
npm ci --production
npm run client-install

# Build the client
npm run build --prefix client

# Restart the application
pm2 restart csfportal-staging
```

### Production Environment

```bash
# SSH into the production server
ssh <EMAIL>

# Navigate to the application directory
cd /var/www/csfportal

# Pull the latest changes
git pull origin main

# Install dependencies
npm ci --production
npm run client-install

# Build the client
npm run build --prefix client

# Restart the application
pm2 restart csfportal
```

## Rollback Procedures

In case of deployment issues, follow these rollback procedures:

### Automated Rollback

The CI/CD pipeline includes a rollback mechanism that can be triggered manually:

1. Go to the GitHub Actions tab in the repository
2. Find the failed deployment workflow
3. Click on "Re-run jobs" and select "Re-run failed jobs"

### Manual Rollback

If the automated rollback fails or is not available, follow these steps:

```bash
# SSH into the server
ssh <EMAIL>

# Navigate to the application directory
cd /var/www/csfportal

# Check out the previous version
git log --oneline -5  # Find the commit hash of the previous stable version
git checkout <commit-hash>

# Install dependencies
npm ci --production
npm run client-install

# Build the client
npm run build --prefix client

# Restart the application
pm2 restart csfportal
```

## Database Rollback

If the deployment included database changes:

```bash
# SSH into the server
ssh <EMAIL>

# Restore the database from the pre-deployment backup
mongorestore --host localhost --port 27017 \
  --username admin --password secure_password \
  --authenticationDatabase admin \
  --db csfportal \
  --drop \
  /path/to/backup/csfportal
```

## Post-Deployment Verification

After each deployment, perform the following verification steps:

### Automated Verification

The CI/CD pipeline includes automated smoke tests that run after deployment:

```yaml
- name: Run smoke tests
  run: |
    npm run test:smoke
```

### Manual Verification

For production deployments, perform these additional manual verification steps:

1. **Basic Functionality**
   - Verify that the application loads correctly
   - Test user login and authentication
   - Check that the dashboard displays correctly

2. **Feature Verification**
   - Test all features that were changed in this deployment
   - Verify that existing features still work correctly

3. **Integration Verification**
   - Test all third-party integrations (Planning Center, Synology, etc.)
   - Verify that data is flowing correctly between systems

4. **Performance Check**
   - Monitor application response times
   - Check server resource usage (CPU, memory, disk)

## Deployment Checklist

Use this checklist for production deployments:

### Pre-Deployment

- [ ] All tests passing in CI/CD pipeline
- [ ] Code review completed and approved
- [ ] Database backup created
- [ ] Deployment plan communicated to stakeholders
- [ ] Maintenance window scheduled (if needed)
- [ ] Rollback plan prepared

### Deployment

- [ ] Trigger deployment in CI/CD pipeline or perform manual deployment
- [ ] Monitor deployment progress
- [ ] Check for any errors or warnings

### Post-Deployment

- [ ] Run automated verification tests
- [ ] Perform manual verification
- [ ] Monitor application logs for errors
- [ ] Monitor system performance
- [ ] Verify all integrations are working
- [ ] Update documentation (if needed)
- [ ] Notify stakeholders of successful deployment

## Deployment Schedule

Production deployments follow this schedule:

| Type | Frequency | Timing | Approval Required |
|------|-----------|--------|-------------------|
| Regular Releases | Bi-weekly | Tuesdays, 8:00 PM EST | Yes |
| Hotfixes | As needed | Any time | Yes |
| Emergency Fixes | As needed | Any time | Yes |

## Deployment Approval Process

For production deployments:

1. Create a deployment request in the project management system
2. Include the list of changes and potential impact
3. Get approval from the project manager and technical lead
4. Schedule the deployment in the deployment calendar
5. Notify all stakeholders of the upcoming deployment

## Deployment Documentation

After each production deployment, update the deployment log:

```
Date: YYYY-MM-DD
Version: X.Y.Z
Deployed by: [Name]
Changes:
- Feature A added
- Bug B fixed
- Performance improvement C

Issues encountered:
- [List any issues encountered during deployment]

Resolution:
- [How issues were resolved]

Verification results:
- [Results of post-deployment verification]
```

Store this log in the project documentation repository for future reference.