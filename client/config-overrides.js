const path = require('path');

module.exports = function override(config, env) {
  // Override webpack-dev-server configuration to use setupMiddlewares instead of deprecated options
  const overrideDevServer = (configFunction) => {
    return (proxy, allowedHost) => {
      const config = configFunction(proxy, allowedHost);

      // Replace deprecated options with setupMiddlewares
      if (config.onBeforeSetupMiddleware || config.onAfterSetupMiddleware) {
        const onBeforeSetupMiddleware = config.onBeforeSetupMiddleware;
        const onAfterSetupMiddleware = config.onAfterSetupMiddleware;

        delete config.onBeforeSetupMiddleware;
        delete config.onAfterSetupMiddleware;

        config.setupMiddlewares = (middlewares, devServer) => {
          // Call the original onBeforeSetupMiddleware if it exists
          if (typeof onBeforeSetupMiddleware === 'function') {
            onBeforeSetupMiddleware(devServer);
          }

          // Return middlewares
          if (typeof onAfterSetupMiddleware === 'function') {
            onAfterSetupMiddleware(devServer);
          }

          return middlewares;
        };
      }

      return config;
    };
  };

  // Return the modified webpack config
  return {
    ...config,
    devServer: {
      setupMiddlewares: (middlewares, devServer) => {
        return middlewares;
      }
    }
  };
};
