import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  CircularProgress, 
  Alert, 
  Button,
  Breadcrumbs,
  Link,
  Divider
} from '@mui/material';
import { 
  ArrowBack as BackIcon,
  OpenInNew as OpenInNewIcon,
  Description as FileIcon,
  Image as ImageIcon,
  Movie as VideoIcon,
  AudioFile as AudioIcon,
  PictureAsPdf as PdfIcon,
  TableChart as SpreadsheetIcon,
  Slideshow as PresentationIcon,
  Code as CodeIcon,
  InsertDriveFile as GenericFileIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import googleDriveService from '../../services/googleDriveService';
import moment from 'moment';

const GoogleDriveViewerPage = () => {
  const { fileId } = useParams();
  const navigate = useNavigate();
  const [file, setFile] = useState(null);
  const [viewerUrl, setViewerUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFileAndViewer = async () => {
      try {
        setLoading(true);
        
        // Get file metadata
        const fileData = await googleDriveService.getFile(fileId);
        setFile(fileData);
        
        // Get embedded viewer URL
        const url = await googleDriveService.getEmbeddedViewerUrl(fileId);
        setViewerUrl(url);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching file:', err);
        setError('Failed to load file from Google Drive');
        setLoading(false);
      }
    };

    if (fileId) {
      fetchFileAndViewer();
    }
  }, [fileId]);

  // Navigate back to files page
  const handleGoBack = () => {
    navigate('/google-drive/files');
  };

  // Get appropriate icon based on file mime type
  const getFileIcon = (mimeType) => {
    if (!mimeType) return <GenericFileIcon fontSize="large" />;
    if (mimeType.includes('image')) return <ImageIcon fontSize="large" />;
    if (mimeType.includes('video')) return <VideoIcon fontSize="large" />;
    if (mimeType.includes('audio')) return <AudioIcon fontSize="large" />;
    if (mimeType.includes('pdf')) return <PdfIcon fontSize="large" />;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return <SpreadsheetIcon fontSize="large" />;
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return <PresentationIcon fontSize="large" />;
    if (mimeType.includes('document') || mimeType.includes('word')) return <FileIcon fontSize="large" />;
    if (mimeType.includes('text') || mimeType.includes('code')) return <CodeIcon fontSize="large" />;
    return <GenericFileIcon fontSize="large" />;
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
  };

  // Check if file is viewable in iframe
  const isViewableInIframe = (mimeType) => {
    if (!mimeType) return false;
    
    const viewableTypes = [
      'application/pdf',
      'application/vnd.google-apps.document',
      'application/vnd.google-apps.spreadsheet',
      'application/vnd.google-apps.presentation',
      'application/vnd.google-apps.drawing',
      'application/vnd.google-apps.file',
      'text/plain',
      'text/html',
      'text/csv'
    ];
    
    return viewableTypes.some(type => mimeType.includes(type));
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              underline="hover"
              color="inherit"
              sx={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
              onClick={handleGoBack}
            >
              <BackIcon sx={{ mr: 0.5 }} fontSize="small" />
              Back to Files
            </Link>
            <Typography color="text.primary">
              {file ? file.name : 'Loading...'}
            </Typography>
          </Breadcrumbs>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        ) : (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Box sx={{ mr: 2 }}>
                {getFileIcon(file?.mimeType)}
              </Box>
              <Box>
                <Typography variant="h5" component="h1" gutterBottom>
                  {file?.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatFileSize(file?.size)} • Modified: {moment(file?.modifiedTime).format('MMMM D, YYYY h:mm A')}
                </Typography>
              </Box>
            </Box>

            {file?.webViewLink && (
              <Box sx={{ mb: 3 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<OpenInNewIcon />}
                  href={file.webViewLink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Open in Google Drive
                </Button>
              </Box>
            )}

            <Divider sx={{ my: 3 }} />

            {isViewableInIframe(file?.mimeType) && viewerUrl ? (
              <Box sx={{ width: '100%', height: '600px', border: '1px solid #e0e0e0', borderRadius: 1, overflow: 'hidden' }}>
                <iframe
                  src={viewerUrl}
                  title={file?.name}
                  width="100%"
                  height="100%"
                  frameBorder="0"
                  allowFullScreen
                />
              </Box>
            ) : (
              <Box sx={{ textAlign: 'center', my: 8 }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  Preview not available
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  This file type cannot be previewed in the browser.
                </Typography>
                {file?.webViewLink && (
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<OpenInNewIcon />}
                    href={file.webViewLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{ mt: 2 }}
                  >
                    Open in Google Drive
                  </Button>
                )}
              </Box>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleDriveViewerPage;