import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  Alert,
  CircularProgress,
  Link,
  Divider
} from '@mui/material';
import { Save as SaveIcon, Settings as SettingsIcon } from '@mui/icons-material';
import googleDriveService from '../../services/googleDriveService';

const GoogleDriveConfigPage = () => {
  const [config, setConfig] = useState({
    clientId: '',
    clientSecret: '',
    redirectUri: window.location.origin + '/api/google-drive/callback',
    tokenPath: './google-drive-token.json'
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [authUrl, setAuthUrl] = useState(null);

  // Fetch current configuration
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const configData = await googleDriveService.getConfig();
        
        if (configData) {
          setConfig(prevConfig => ({
            ...prevConfig,
            clientId: configData.clientId || '',
            redirectUri: configData.redirectUri || window.location.origin + '/api/google-drive/callback',
            tokenPath: configData.tokenPath || './google-drive-token.json'
          }));
          setConfigStatus(configData);
          
          // If not authenticated, get auth URL
          if (!configData.isAuthenticated) {
            try {
              const url = await googleDriveService.getAuthUrl();
              setAuthUrl(url);
            } catch (err) {
              console.error('Error getting auth URL:', err);
            }
          }
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching configuration:', err);
        setError('Failed to load Google Drive configuration');
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setConfig(prevConfig => ({
      ...prevConfig,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      await googleDriveService.saveConfig(config);
      
      // Fetch updated config status
      const configData = await googleDriveService.getConfig();
      setConfigStatus(configData);
      
      // If not authenticated, get auth URL
      if (!configData.isAuthenticated) {
        try {
          const url = await googleDriveService.getAuthUrl();
          setAuthUrl(url);
        } catch (err) {
          console.error('Error getting auth URL:', err);
        }
      }
      
      setSuccess('Google Drive configuration saved successfully');
      setSaving(false);
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save Google Drive configuration');
      setSaving(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Google Drive Configuration
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure the Google Drive integration by providing your Google API credentials.
          </Typography>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}
            
            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}
            
            {configStatus && (
              <Box sx={{ mb: 4 }}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle1">
                    Current Configuration Status
                  </Typography>
                  <Typography variant="body2">
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                  <Typography variant="body2">
                    Authentication status: {configStatus.isAuthenticated ? 'Authenticated' : 'Not authenticated'}
                  </Typography>
                </Alert>
                
                {!configStatus.isAuthenticated && authUrl && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      To complete the authentication process, click the link below:
                    </Typography>
                    <Link href={authUrl} target="_blank" rel="noopener noreferrer">
                      Authenticate with Google Drive
                    </Link>
                  </Box>
                )}
              </Box>
            )}
            
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    label="Client ID"
                    name="clientId"
                    value={config.clientId}
                    onChange={handleChange}
                    fullWidth
                    required
                    helperText="From Google Cloud Console"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    label="Client Secret"
                    name="clientSecret"
                    value={config.clientSecret}
                    onChange={handleChange}
                    fullWidth
                    required
                    type="password"
                    helperText="From Google Cloud Console"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    label="Redirect URI"
                    name="redirectUri"
                    value={config.redirectUri}
                    onChange={handleChange}
                    fullWidth
                    required
                    helperText="Must match the redirect URI configured in Google Cloud Console"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    label="Token Path"
                    name="tokenPath"
                    value={config.tokenPath}
                    onChange={handleChange}
                    fullWidth
                    helperText="Path where the OAuth token will be stored (default: ./google-drive-token.json)"
                  />
                </Grid>
              </Grid>
              
              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save Configuration'}
                </Button>
              </Box>
            </form>
            
            <Divider sx={{ my: 4 }} />
            
            <Box>
              <Typography variant="h6" gutterBottom>
                Setup Instructions
              </Typography>
              <Typography variant="body2" paragraph>
                1. Go to the <Link href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer">Google Cloud Console</Link>
              </Typography>
              <Typography variant="body2" paragraph>
                2. Create a new project or select an existing one
              </Typography>
              <Typography variant="body2" paragraph>
                3. Navigate to "APIs & Services" > "Library" and enable the Google Drive API
              </Typography>
              <Typography variant="body2" paragraph>
                4. Go to "APIs & Services" > "Credentials" and create an OAuth 2.0 Client ID
              </Typography>
              <Typography variant="body2" paragraph>
                5. Add "{window.location.origin}/api/google-drive/callback" as an authorized redirect URI
              </Typography>
              <Typography variant="body2" paragraph>
                6. Copy the Client ID and Client Secret and paste them in the form above
              </Typography>
              <Typography variant="body2" paragraph>
                7. Save the configuration and click the authentication link that appears
              </Typography>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleDriveConfigPage;