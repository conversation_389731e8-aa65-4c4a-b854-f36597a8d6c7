import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Paper,
  Breadcrumbs,
  Link
} from '@mui/material';
import { 
  Search as SearchIcon,
  Folder as FolderIcon,
  Description as FileIcon,
  Image as ImageIcon,
  Movie as VideoIcon,
  AudioFile as AudioIcon,
  PictureAsPdf as PdfIcon,
  TableChart as SpreadsheetIcon,
  Slideshow as PresentationIcon,
  Code as CodeIcon,
  InsertDriveFile as GenericFileIcon,
  ArrowBack as BackIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import googleDriveService from '../../services/googleDriveService';
import moment from 'moment';

const GoogleDriveFilesPage = () => {
  const navigate = useNavigate();
  const [files, setFiles] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searching, setSearching] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);

  // Check configuration status
  useEffect(() => {
    const checkConfig = async () => {
      try {
        const config = await googleDriveService.getConfig();
        setConfigStatus(config);
        
        if (config && config.isAuthenticated) {
          fetchFiles();
        } else {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error checking configuration:', err);
        setError('Failed to check Google Drive configuration');
        setLoading(false);
      }
    };

    checkConfig();
  }, []);

  // Fetch files from Google Drive
  const fetchFiles = async () => {
    try {
      setLoading(true);
      const data = await googleDriveService.listFiles();
      setFiles(data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching files:', err);
      setError('Failed to load files from Google Drive');
      setLoading(false);
    }
  };

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle search submission
  const handleSearch = async (event) => {
    event.preventDefault();
    
    if (!searchTerm.trim()) {
      return;
    }
    
    try {
      setSearching(true);
      const data = await googleDriveService.searchFiles(searchTerm);
      setFiles(data);
      setSearching(false);
    } catch (err) {
      console.error('Error searching files:', err);
      setError('Failed to search Google Drive');
      setSearching(false);
    }
  };

  // Reset search and fetch all files
  const handleResetSearch = async () => {
    setSearchTerm('');
    fetchFiles();
  };

  // Navigate to configuration page
  const handleGoToConfig = () => {
    navigate('/google-drive/config');
  };

  // Navigate to viewer page
  const handleViewFile = (fileId) => {
    navigate(`/google-drive/view/${fileId}`);
  };

  // Get appropriate icon based on file mime type
  const getFileIcon = (mimeType) => {
    if (mimeType.includes('folder')) return <FolderIcon />;
    if (mimeType.includes('image')) return <ImageIcon />;
    if (mimeType.includes('video')) return <VideoIcon />;
    if (mimeType.includes('audio')) return <AudioIcon />;
    if (mimeType.includes('pdf')) return <PdfIcon />;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return <SpreadsheetIcon />;
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return <PresentationIcon />;
    if (mimeType.includes('document') || mimeType.includes('word')) return <FileIcon />;
    if (mimeType.includes('text') || mimeType.includes('code')) return <CodeIcon />;
    return <GenericFileIcon />;
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Google Drive Files
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Browse and search files in Google Drive
          </Typography>
        </Box>

        {!configStatus || !configStatus.isAuthenticated ? (
          <Box sx={{ my: 4 }}>
            <Alert severity="warning" sx={{ mb: 3 }}>
              Google Drive is not configured or authenticated. Please configure it first.
            </Alert>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SettingsIcon />}
              onClick={handleGoToConfig}
            >
              Configure Google Drive
            </Button>
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSearch} sx={{ mb: 4 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={9}>
                  <TextField
                    fullWidth
                    label="Search Files"
                    value={searchTerm}
                    onChange={handleSearchChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                      endAdornment: searching && (
                        <InputAdornment position="end">
                          <CircularProgress size={24} />
                        </InputAdornment>
                      )
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ display: 'flex', gap: 1, height: '100%' }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={searching || !searchTerm.trim()}
                      sx={{ flex: 1 }}
                    >
                      Search
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleResetSearch}
                      disabled={searching || !searchTerm.trim()}
                      sx={{ flex: 1 }}
                    >
                      Reset
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                {files.length === 0 ? (
                  <Box sx={{ textAlign: 'center', my: 8 }}>
                    <Typography variant="h6" color="text.secondary">
                      No files found
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {files.map((file) => (
                      <ListItem
                        key={file.id}
                        disablePadding
                        divider
                      >
                        <ListItemButton onClick={() => handleViewFile(file.id)}>
                          <ListItemIcon>
                            {getFileIcon(file.mimeType)}
                          </ListItemIcon>
                          <ListItemText
                            primary={file.name}
                            secondary={
                              <>
                                <Typography component="span" variant="body2" color="text.primary">
                                  {formatFileSize(file.size)}
                                </Typography>
                                {' • '}
                                <Typography component="span" variant="body2" color="text.primary">
                                  Modified: {moment(file.modifiedTime).format('MMM D, YYYY')}
                                </Typography>
                              </>
                            }
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                )}
              </>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleDriveFilesPage;