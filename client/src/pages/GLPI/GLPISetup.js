import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip,
  FormControlLabel,
  Switch
} from '@mui/material';
import glpiService from '../../services/glpiService';

const GLPISetup = () => {
  const [formData, setFormData] = useState({
    url: '',
    appToken: '',
    userToken: '',
    username: '',
    password: '',
    useUserToken: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [testingConnection, setTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState(null);

  const { url, appToken, userToken, username, password, useUserToken } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await glpiService.getConfig();
        setConfigStatus(config);
        if (config) {
          setFormData({
            ...formData,
            url: config.url || '',
            appToken: config.appToken || '',
            userToken: config.userToken || '',
            username: config.username || '',
            useUserToken: config.userToken ? true : false
          });
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setFormData({ ...formData, [e.target.name]: value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Only send password if it's not empty (to avoid overwriting with empty password)
      const configToSave = { ...formData };
      if (!configToSave.password) {
        delete configToSave.password;
      }

      await glpiService.saveConfig(configToSave);
      setSuccess(true);
      
      // Refresh the config status
      const config = await glpiService.getConfig();
      setConfigStatus(config);
      
      // Clear the password field
      setFormData({
        ...formData,
        password: ''
      });
    } catch (err) {
      setError('Failed to save GLPI configuration. Please try again.');
      console.error('Error saving GLPI configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setTestingConnection(true);
    setTestResult(null);
    setError(null);

    try {
      const result = await glpiService.testConnection();
      setTestResult({
        success: true,
        message: result.message,
        details: result.details
      });
    } catch (err) {
      setTestResult({
        success: false,
        message: err.response?.data?.message || 'Connection test failed',
        details: err.response?.data?.error || err.message
      });
    } finally {
      setTestingConnection(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          GLPI Asset Management Setup
        </Typography>
        
        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    GLPI is configured with URL: {configStatus.url}
                    <br />
                    Authentication: {configStatus.userToken ? 'User Token' : 'Username/Password'}
                    <br />
                    {configStatus.username && `Username: ${configStatus.username}`}
                    <br />
                    Last updated: {new Date(configStatus.updatedAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    GLPI integration is not configured yet. Please provide your GLPI credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Typography variant="body1" paragraph>
            To integrate with GLPI Asset Management, you need to provide your GLPI server details and authentication credentials.
          </Typography>
          
          <Typography variant="body1" paragraph>
            Enter the following information:
          </Typography>
          
          <ul>
            <li>
              <Typography variant="body1" paragraph>
                URL: The full URL to your GLPI instance (e.g., https://glpi.example.com)
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                App Token: Your GLPI application token
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                User Token or Username/Password: Your GLPI authentication credentials
              </Typography>
            </li>
          </ul>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              GLPI configuration saved successfully!
            </Alert>
          )}

          {testResult && (
            <Alert severity={testResult.success ? "success" : "error"} sx={{ mb: 2 }}>
              <Typography variant="body1">{testResult.message}</Typography>
              {testResult.details && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {testResult.details}
                </Typography>
              )}
            </Alert>
          )}
          
          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="url"
              label="GLPI URL"
              name="url"
              value={url}
              onChange={onChange}
              autoFocus
              placeholder="e.g., https://glpi.example.com"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="appToken"
              label="App Token"
              name="appToken"
              value={appToken}
              onChange={onChange}
              placeholder="Your GLPI application token"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={useUserToken}
                  onChange={onChange}
                  name="useUserToken"
                  color="primary"
                />
              }
              label="Use User Token for authentication (recommended)"
            />
            
            {useUserToken ? (
              <TextField
                margin="normal"
                required
                fullWidth
                id="userToken"
                label="User Token"
                name="userToken"
                value={userToken}
                onChange={onChange}
                placeholder="Your GLPI user token"
              />
            ) : (
              <>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="username"
                  label="Username"
                  name="username"
                  value={username}
                  onChange={onChange}
                  placeholder="Your GLPI username"
                />
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type="password"
                  id="password"
                  value={password}
                  onChange={onChange}
                  placeholder="Your GLPI password"
                />
              </>
            )}
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3, mb: 2 }}>
              <Button
                variant="outlined"
                onClick={testConnection}
                disabled={loading || testingConnection || !url || !appToken || (useUserToken && !userToken) || (!useUserToken && (!username || !password))}
              >
                {testingConnection ? <CircularProgress size={24} /> : 'Test Connection'}
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                disabled={loading || !url || !appToken || (useUserToken && !userToken) || (!useUserToken && (!username))}
              >
                {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
              </Button>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default GLPISetup;