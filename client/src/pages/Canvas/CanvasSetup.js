import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import canvasService from '../../services/canvasService';

const CanvasSetup = () => {
  const [formData, setFormData] = useState({
    domain: '',
    apiKey: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  const { domain, apiKey } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await canvasService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await canvasService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await canvasService.getConfig();
      setConfigStatus(config);

      // Clear the form
      setFormData({
        domain: '',
        apiKey: ''
      });
    } catch (err) {
      setError('Failed to save Canvas configuration. Please try again.');
      console.error('Error saving Canvas configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Canvas LMS Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Canvas LMS is configured with domain: {configStatus.domain}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Canvas LMS integration is not configured yet. Please provide your API credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Typography variant="body1" paragraph>
            To integrate with Canvas LMS, you need to provide your Canvas domain and API key. 
          </Typography>

          <Typography variant="body1" paragraph>
            Follow these steps to get your API key:
          </Typography>

          <ol>
            <li>
              <Typography variant="body1" paragraph>
                Log in to your Canvas LMS account
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Go to Account → Settings
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Scroll down to the Approved Integrations section
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Click on "+ New Access Token"
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Enter a purpose (e.g., "CSF Portal Integration") and click "Generate Token"
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Copy the generated token (this is your API key)
              </Typography>
            </li>
          </ol>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Canvas LMS configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="domain"
              label="Canvas Domain (e.g., canvas.instructure.com)"
              name="domain"
              value={domain}
              onChange={onChange}
              autoFocus
              helperText="Enter only the domain name without https:// or trailing slashes"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="apiKey"
              label="API Key"
              type="password"
              id="apiKey"
              value={apiKey}
              onChange={onChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default CanvasSetup;