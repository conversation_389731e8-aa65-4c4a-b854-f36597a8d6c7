import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import FolderIcon from '@mui/icons-material/Folder';
import DescriptionIcon from '@mui/icons-material/Description';
import PersonIcon from '@mui/icons-material/Person';
import SchoolIcon from '@mui/icons-material/School';
import canvasService from '../../services/canvasService';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`canvas-tabpanel-${index}`}
      aria-labelledby={`canvas-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const CanvasPage = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [courses, setCourses] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [courseFiles, setCourseFiles] = useState([]);
  const [loadingFiles, setLoadingFiles] = useState(false);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle course selection for file browsing
  const handleCourseSelect = async (course) => {
    setSelectedCourse(course);
    setLoadingFiles(true);
    setError(null);
    
    try {
      const files = await canvasService.getCourseFiles(course.id);
      setCourseFiles(files);
    } catch (err) {
      setError(`Failed to load files for course ${course.name}. Please try again.`);
      console.error('Error loading course files:', err);
    } finally {
      setLoadingFiles(false);
    }
  };

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        switch (tabValue) {
          case 0: // Courses
            if (courses.length === 0) {
              const coursesData = await canvasService.getCourses();
              setCourses(coursesData);
            }
            break;
          case 1: // Files
            if (courses.length === 0) {
              const coursesData = await canvasService.getCourses();
              setCourses(coursesData);
            }
            break;
          case 2: // Users
            if (users.length === 0) {
              const usersData = await canvasService.getUsers();
              setUsers(usersData);
            }
            break;
          default:
            break;
        }
      } catch (err) {
        setError('Failed to load data from Canvas LMS. Please check your connection and try again.');
        console.error('Error loading Canvas data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, courses.length, users.length]);

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Canvas LMS
        </Typography>
        
        <Paper sx={{ width: '100%', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            centered
          >
            <Tab label="Courses" />
            <Tab label="Files" />
            <Tab label="Users" />
          </Tabs>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TabPanel value={tabValue} index={0}>
                <Typography variant="h6">Courses</Typography>
                {courses.length > 0 ? (
                  <List>
                    {courses.map((course) => (
                      <ListItem key={course.id}>
                        <ListItemIcon>
                          <SchoolIcon />
                        </ListItemIcon>
                        <ListItemText 
                          primary={course.name} 
                          secondary={course.course_code} 
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography>No courses found</Typography>
                )}
              </TabPanel>
              
              <TabPanel value={tabValue} index={1}>
                <Typography variant="h6">Files</Typography>
                {courses.length > 0 ? (
                  <Box sx={{ display: 'flex' }}>
                    <Box sx={{ width: '30%', borderRight: 1, borderColor: 'divider', pr: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>Select a Course</Typography>
                      <List>
                        {courses.map((course) => (
                          <ListItem 
                            button 
                            key={course.id}
                            onClick={() => handleCourseSelect(course)}
                            selected={selectedCourse && selectedCourse.id === course.id}
                          >
                            <ListItemIcon>
                              <FolderIcon />
                            </ListItemIcon>
                            <ListItemText primary={course.name} />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                    <Box sx={{ width: '70%', pl: 2 }}>
                      {selectedCourse ? (
                        <>
                          <Typography variant="subtitle1" gutterBottom>
                            Files for {selectedCourse.name}
                          </Typography>
                          {loadingFiles ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                              <CircularProgress />
                            </Box>
                          ) : (
                            <>
                              {courseFiles.length > 0 ? (
                                <List>
                                  {courseFiles.map((file) => (
                                    <ListItem key={file.id}>
                                      <ListItemIcon>
                                        <DescriptionIcon />
                                      </ListItemIcon>
                                      <ListItemText 
                                        primary={file.display_name} 
                                        secondary={`Size: ${(file.size / 1024).toFixed(2)} KB`} 
                                      />
                                    </ListItem>
                                  ))}
                                </List>
                              ) : (
                                <Typography>No files found for this course</Typography>
                              )}
                            </>
                          )}
                        </>
                      ) : (
                        <Typography>Select a course to view files</Typography>
                      )}
                    </Box>
                  </Box>
                ) : (
                  <Typography>No courses found</Typography>
                )}
              </TabPanel>
              
              <TabPanel value={tabValue} index={2}>
                <Typography variant="h6">Users</Typography>
                {users.length > 0 ? (
                  <List>
                    {users.map((user) => (
                      <ListItem key={user.id}>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText 
                          primary={`${user.name}`} 
                          secondary={user.email} 
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography>No users found</Typography>
                )}
              </TabPanel>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default CanvasPage;