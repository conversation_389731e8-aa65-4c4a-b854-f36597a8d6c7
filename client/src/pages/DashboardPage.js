import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  Link as LinkIcon,
  Folder as FolderIcon,
  Description as FileIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const DashboardPage = () => {
  const { user } = useAuth();
  const [shortcuts, setShortcuts] = useState([]);
  const [recentFiles, setRecentFiles] = useState([]);
  const [loading, setLoading] = useState({ shortcuts: true, files: true });
  const [error, setError] = useState({ shortcuts: null, files: null });

  // Fetch shortcuts
  useEffect(() => {
    const fetchShortcuts = async () => {
      try {
        const res = await axios.get('/api/shortcuts');
        // Sort by click count to get most popular
        const sortedShortcuts = res.data.sort((a, b) => b.clickCount - a.clickCount);
        setShortcuts(sortedShortcuts.slice(0, 5)); // Get top 5
        setLoading(prev => ({ ...prev, shortcuts: false }));
      } catch (err) {
        console.error('Error fetching shortcuts:', err);
        setError(prev => ({ ...prev, shortcuts: 'Failed to load shortcuts' }));
        setLoading(prev => ({ ...prev, shortcuts: false }));
      }
    };

    fetchShortcuts();
  }, []);

  // Fetch recent files from Google Drive
  useEffect(() => {
    const fetchRecentFiles = async () => {
      try {
        const res = await axios.get('/api/google/drive/files');
        setRecentFiles(res.data.slice(0, 5)); // Get top 5
        setLoading(prev => ({ ...prev, files: false }));
      } catch (err) {
        console.error('Error fetching files:', err);
        setError(prev => ({ ...prev, files: 'Failed to load recent files' }));
        setLoading(prev => ({ ...prev, files: false }));
      }
    };

    fetchRecentFiles();
  }, []);

  // Track shortcut clicks
  const handleShortcutClick = async (id) => {
    try {
      await axios.post(`/api/shortcuts/${id}/click`);
    } catch (err) {
      console.error('Error tracking shortcut click:', err);
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome, {user?.name}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Here's your dashboard with quick access to important resources
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Popular Shortcuts */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" component="h2">
                Popular Shortcuts
              </Typography>
              <Button 
                component={RouterLink} 
                to="/shortcuts" 
                size="small"
              >
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            
            {loading.shortcuts ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : error.shortcuts ? (
              <Alert severity="error">{error.shortcuts}</Alert>
            ) : shortcuts.length === 0 ? (
              <Alert severity="info">No shortcuts available</Alert>
            ) : (
              <List>
                {shortcuts.map((shortcut) => (
                  <ListItem 
                    key={shortcut._id}
                    component="a"
                    href={shortcut.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => handleShortcutClick(shortcut._id)}
                    button
                    divider
                  >
                    <ListItemIcon>
                      <LinkIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary={shortcut.title} 
                      secondary={shortcut.description}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Recent Files */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" component="h2">
                Recent Files
              </Typography>
              <Button 
                component={RouterLink} 
                to="/drive" 
                size="small"
              >
                View All
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            
            {loading.files ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : error.files ? (
              <Alert severity="error">{error.files}</Alert>
            ) : recentFiles.length === 0 ? (
              <Alert severity="info">No recent files</Alert>
            ) : (
              <List>
                {recentFiles.map((file) => (
                  <ListItem 
                    key={file.id}
                    component="a"
                    href={file.webViewLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    button
                    divider
                  >
                    <ListItemIcon>
                      {file.mimeType.includes('folder') ? <FolderIcon /> : <FileIcon />}
                    </ListItemIcon>
                    <ListItemText 
                      primary={file.name} 
                      secondary={new Date(file.modifiedTime).toLocaleDateString()}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Paper elevation={2} sx={{ p: 2 }}>
            <Typography variant="h6" component="h2" gutterBottom>
              Quick Actions
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Card className="shortcut-card">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <LinkIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                      <Typography variant="h6" component="h3" align="center">
                        Shortcuts
                      </Typography>
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button 
                      fullWidth 
                      component={RouterLink} 
                      to="/shortcuts"
                    >
                      View
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card className="shortcut-card">
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <FolderIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                      <Typography variant="h6" component="h3" align="center">
                        Drive Files
                      </Typography>
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button 
                      fullWidth 
                      component={RouterLink} 
                      to="/drive"
                    >
                      View
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
              {user?.roles.includes('admin') && (
                <>
                  <Grid item xs={6} sm={3}>
                    <Card className="shortcut-card">
                      <CardContent>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                          <StarIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                          <Typography variant="h6" component="h3" align="center">
                            Admin
                          </Typography>
                        </Box>
                      </CardContent>
                      <CardActions>
                        <Button 
                          fullWidth 
                          component={RouterLink} 
                          to="/admin/users"
                        >
                          View
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                </>
              )}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DashboardPage;