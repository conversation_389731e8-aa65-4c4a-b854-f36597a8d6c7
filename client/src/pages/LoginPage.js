import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Button, 
  Container, 
  Typography, 
  Paper, 
  Divider,
  Alert
} from '@mui/material';
import { Google as GoogleIcon } from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const LoginPage = () => {
  const { user, loading, error, loginWithGoogle } = useAuth();
  const navigate = useNavigate();

  // Redirect if already logged in
  useEffect(() => {
    if (user && !loading) {
      navigate('/dashboard');
    }
  }, [user, loading, navigate]);

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8, mb: 4 }}>
        <Typography variant="h4" component="h1" align="center" gutterBottom>
          Welcome to Church Staff Portal
        </Typography>
        <Typography variant="body1" align="center" color="text.secondary">
          Please sign in with your church Google account to continue
        </Typography>
      </Box>

      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center' 
        }}
      >
        {error && (
          <Alert severity="error" sx={{ width: '100%', mb: 3 }}>
            {error}
          </Alert>
        )}

        <Typography variant="h5" component="h2" gutterBottom>
          Sign In
        </Typography>
        
        <Box sx={{ width: '100%', mt: 2 }}>
          <Button
            fullWidth
            variant="contained"
            color="primary"
            size="large"
            startIcon={<GoogleIcon />}
            onClick={loginWithGoogle}
            disabled={loading}
            sx={{ py: 1.5 }}
          >
            Sign in with Google
          </Button>
        </Box>

        <Box sx={{ width: '100%', mt: 3 }}>
          <Divider>
            <Typography variant="body2" color="text.secondary">
              Important Information
            </Typography>
          </Divider>
          
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" paragraph>
              • You must use your church email address to sign in
            </Typography>
            <Typography variant="body2" paragraph>
              • This portal is for authorized church staff only
            </Typography>
            <Typography variant="body2" paragraph>
              • If you have trouble signing in, please contact the IT department
            </Typography>
          </Box>
        </Box>
      </Paper>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          By signing in, you agree to the terms of service and privacy policy.
        </Typography>
      </Box>
    </Container>
  );
};

export default LoginPage;