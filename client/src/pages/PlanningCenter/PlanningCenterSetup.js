import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import planningCenterService from '../../services/planningCenterService';

const PlanningCenterSetup = () => {
  const [formData, setFormData] = useState({
    clientId: '',
    clientSecret: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  const { clientId, clientSecret } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await planningCenterService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await planningCenterService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await planningCenterService.getConfig();
      setConfigStatus(config);

      // Clear the form
      setFormData({
        clientId: '',
        clientSecret: ''
      });
    } catch (err) {
      setError('Failed to save Planning Center configuration. Please try again.');
      console.error('Error saving Planning Center configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Planning Center Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Planning Center is configured with Application ID: {configStatus.clientId}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Planning Center integration is not configured yet. Please provide your API credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Typography variant="body1" paragraph>
            To integrate with Planning Center, you need to provide your API credentials. 
            You can find these in your Planning Center account under Developer API Access.
          </Typography>

          <Typography variant="body1" paragraph>
            Follow these steps to get your credentials:
          </Typography>

          <ol>
            <li>
              <Typography variant="body1" paragraph>
                Log in to your Planning Center account
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Go to Developer API Access in your account settings
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Create a new application or use an existing one
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Copy the Application ID and Secret
              </Typography>
            </li>
          </ol>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Planning Center configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="clientId"
              label="Application ID"
              name="clientId"
              value={clientId}
              onChange={onChange}
              autoFocus
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="clientSecret"
              label="Application Secret"
              type="password"
              id="clientSecret"
              value={clientSecret}
              onChange={onChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default PlanningCenterSetup;
