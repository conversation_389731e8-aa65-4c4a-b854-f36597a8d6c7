import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert
} from '@mui/material';
import planningCenterService from '../../services/planningCenterService';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`planning-center-tabpanel-${index}`}
      aria-labelledby={`planning-center-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const PlanningCenterPage = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [events, setEvents] = useState([]);
  const [people, setPeople] = useState([]);
  const [resources, setResources] = useState([]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        switch (tabValue) {
          case 0: // Events
            if (events.length === 0) {
              const eventsData = await planningCenterService.getEvents();
              setEvents(eventsData);
            }
            break;
          case 1: // People
            if (people.length === 0) {
              const peopleData = await planningCenterService.getPeople();
              setPeople(peopleData);
            }
            break;
          case 2: // Resources
            if (resources.length === 0) {
              const resourcesData = await planningCenterService.getResources();
              setResources(resourcesData);
            }
            break;
          default:
            break;
        }
      } catch (err) {
        setError('Failed to load data from Planning Center. Please check your connection and try again.');
        console.error('Error loading Planning Center data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, events.length, people.length, resources.length]);

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Planning Center
        </Typography>
        
        <Paper sx={{ width: '100%', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            centered
          >
            <Tab label="Events" />
            <Tab label="People" />
            <Tab label="Resources" />
          </Tabs>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TabPanel value={tabValue} index={0}>
                <Typography variant="h6">Calendar Events</Typography>
                {events.length > 0 ? (
                  <pre>{JSON.stringify(events, null, 2)}</pre>
                ) : (
                  <Typography>No events found</Typography>
                )}
              </TabPanel>
              
              <TabPanel value={tabValue} index={1}>
                <Typography variant="h6">People</Typography>
                {people.length > 0 ? (
                  <pre>{JSON.stringify(people, null, 2)}</pre>
                ) : (
                  <Typography>No people found</Typography>
                )}
              </TabPanel>
              
              <TabPanel value={tabValue} index={2}>
                <Typography variant="h6">Resources</Typography>
                {resources.length > 0 ? (
                  <pre>{JSON.stringify(resources, null, 2)}</pre>
                ) : (
                  <Typography>No resources found</Typography>
                )}
              </TabPanel>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default PlanningCenterPage;