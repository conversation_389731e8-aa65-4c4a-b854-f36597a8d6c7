import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import { 
  Search as SearchIcon,
  Link as LinkIcon,
  Info as InfoIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const ShortcutsPage = () => {
  const { user } = useAuth();
  const [shortcuts, setShortcuts] = useState([]);
  const [filteredShortcuts, setFilteredShortcuts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedShortcut, setSelectedShortcut] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Fetch shortcuts and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch shortcuts
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        setFilteredShortcuts(shortcutsRes.data);
        
        // Fetch categories
        const categoriesRes = await axios.get('/api/shortcuts/categories');
        setCategories(['All', ...categoriesRes.data]);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load shortcuts');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter shortcuts based on category and search term
  useEffect(() => {
    let filtered = shortcuts;
    
    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(shortcut => 
        shortcut.categories.includes(selectedCategory)
      );
    }
    
    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(shortcut => 
        shortcut.title.toLowerCase().includes(term) || 
        (shortcut.description && shortcut.description.toLowerCase().includes(term))
      );
    }
    
    setFilteredShortcuts(filtered);
  }, [selectedCategory, searchTerm, shortcuts]);

  // Handle category selection
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Track shortcut clicks
  const handleShortcutClick = async (id) => {
    try {
      await axios.post(`/api/shortcuts/${id}/click`);
    } catch (err) {
      console.error('Error tracking shortcut click:', err);
    }
  };

  // Open instructions dialog
  const handleOpenDialog = (shortcut) => {
    setSelectedShortcut(shortcut);
    setDialogOpen(true);
  };

  // Close instructions dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Shortcuts
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quick access to important resources and services
        </Typography>
      </Box>

      {/* Search and filter */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search shortcuts..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {categories.map((category) => (
                <Chip
                  key={category}
                  label={category}
                  onClick={() => handleCategoryChange(category)}
                  color={selectedCategory === category ? 'primary' : 'default'}
                  variant={selectedCategory === category ? 'filled' : 'outlined'}
                />
              ))}
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Shortcuts grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : filteredShortcuts.length === 0 ? (
        <Alert severity="info">
          No shortcuts found. Try changing your search or filter.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {filteredShortcuts.map((shortcut) => (
            <Grid item xs={12} sm={6} md={4} key={shortcut._id}>
              <Card className="shortcut-card">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ mr: 1 }}>
                      <LinkIcon color="primary" />
                    </Box>
                    <Typography variant="h6" component="h2" sx={{ flexGrow: 1 }}>
                      {shortcut.title}
                    </Typography>
                    {shortcut.instructions && (
                      <Tooltip title="View Instructions">
                        <IconButton 
                          size="small" 
                          onClick={() => handleOpenDialog(shortcut)}
                        >
                          <InfoIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {shortcut.description}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                    {shortcut.categories.map((category) => (
                      <Chip 
                        key={category} 
                        label={category} 
                        size="small" 
                        variant="outlined"
                        onClick={() => handleCategoryChange(category)}
                      />
                    ))}
                  </Box>
                </CardContent>
                <Divider />
                <CardActions>
                  <Button 
                    fullWidth
                    variant="contained"
                    color="primary"
                    startIcon={<LaunchIcon />}
                    component="a"
                    href={shortcut.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => handleShortcutClick(shortcut._id)}
                  >
                    Open
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Instructions Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="instructions-dialog-title"
      >
        {selectedShortcut && (
          <>
            <DialogTitle id="instructions-dialog-title">
              {selectedShortcut.title} - Instructions
            </DialogTitle>
            <DialogContent>
              <DialogContentText>
                {selectedShortcut.instructions || 'No instructions provided.'}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
              <Button 
                variant="contained" 
                color="primary"
                component="a"
                href={selectedShortcut.url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => {
                  handleShortcutClick(selectedShortcut._id);
                  handleCloseDialog();
                }}
              >
                Open Link
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default ShortcutsPage;