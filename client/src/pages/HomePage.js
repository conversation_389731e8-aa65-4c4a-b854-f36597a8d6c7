import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Box, 
  Button, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  CardMedia
} from '@mui/material';
import { 
  Security as SecurityIcon,
  Group as GroupIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const HomePage = () => {
  const { user, loginWithGoogle } = useAuth();

  return (
    <Box>
      {/* Hero section */}
      <Box 
        sx={{ 
          bgcolor: 'primary.main', 
          color: 'primary.contrastText',
          py: 8,
          mb: 6
        }}
      >
        <Container maxWidth="md">
          <Typography
            component="h1"
            variant="h2"
            align="center"
            gutterBottom
          >
            Church Staff Portal
          </Typography>
          <Typography
            variant="h5"
            align="center"
            paragraph
          >
            Access important information and services for church staff members.
            Securely connect to Google Drive, manage shortcuts, and more.
          </Typography>
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
            {user ? (
              <Button
                variant="contained"
                color="secondary"
                size="large"
                component={RouterLink}
                to="/dashboard"
              >
                Go to Dashboard
              </Button>
            ) : (
              <Button
                variant="contained"
                color="secondary"
                size="large"
                onClick={loginWithGoogle}
              >
                Sign in with Google
              </Button>
            )}
          </Box>
        </Container>
      </Box>

      {/* Features section */}
      <Container maxWidth="md">
        <Typography
          component="h2"
          variant="h3"
          align="center"
          gutterBottom
        >
          Features
        </Typography>
        <Grid container spacing={4} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardMedia
                sx={{
                  p: 2,
                  display: 'flex',
                  justifyContent: 'center'
                }}
              >
                <SecurityIcon color="primary" sx={{ fontSize: 60 }} />
              </CardMedia>
              <CardContent>
                <Typography gutterBottom variant="h5" component="h3">
                  Secure Access
                </Typography>
                <Typography>
                  Login with your Google account for secure access to all church resources.
                  Role-based permissions ensure you see only what you need.
                </Typography>
              </CardContent>
              <CardActions>
                <Button size="small" color="primary">
                  Learn More
                </Button>
              </CardActions>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardMedia
                sx={{
                  p: 2,
                  display: 'flex',
                  justifyContent: 'center'
                }}
              >
                <GroupIcon color="primary" sx={{ fontSize: 60 }} />
              </CardMedia>
              <CardContent>
                <Typography gutterBottom variant="h5" component="h3">
                  Staff Resources
                </Typography>
                <Typography>
                  Quick access to important staff resources, documents, and services.
                  Organized shortcuts make finding what you need easy.
                </Typography>
              </CardContent>
              <CardActions>
                <Button size="small" color="primary">
                  Learn More
                </Button>
              </CardActions>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardMedia
                sx={{
                  p: 2,
                  display: 'flex',
                  justifyContent: 'center'
                }}
              >
                <StorageIcon color="primary" sx={{ fontSize: 60 }} />
              </CardMedia>
              <CardContent>
                <Typography gutterBottom variant="h5" component="h3">
                  Google Integration
                </Typography>
                <Typography>
                  Seamless integration with Google Drive and other Google services.
                  Access your files and collaborate with your team.
                </Typography>
              </CardContent>
              <CardActions>
                <Button size="small" color="primary">
                  Learn More
                </Button>
              </CardActions>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default HomePage;