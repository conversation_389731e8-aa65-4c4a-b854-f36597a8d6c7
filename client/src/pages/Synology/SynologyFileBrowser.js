import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  ListItemButton,
  Breadcrumbs,
  Link,
  Button,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  ArrowUpward as UpIcon,
  Refresh as RefreshIcon,
  Share as ShareIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import synologyService from '../../services/synologyService';

const SynologyFileBrowser = () => {
  const [currentPath, setCurrentPath] = useState('/');
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [sharingLink, setSharingLink] = useState(null);
  const [sharingLoading, setSharingLoading] = useState(false);
  const [sharingError, setSharingError] = useState(null);

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await synologyService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Fetch files when currentPath changes or config is loaded
  useEffect(() => {
    if (configStatus) {
      fetchFiles(currentPath);
    }
  }, [currentPath, configStatus]);

  const fetchFiles = async (path) => {
    setLoading(true);
    setError(null);
    setSharingLink(null);
    setSharingError(null);

    try {
      const fileList = await synologyService.listFiles(path);
      setFiles(fileList);
    } catch (err) {
      console.error('Error fetching files:', err);
      setError('Failed to load files. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFileClick = (file) => {
    if (file.isdir) {
      // Navigate to the folder
      setCurrentPath(file.path);
    } else {
      // For files, do nothing (download is handled by the download button)
    }
  };

  const handleUpClick = () => {
    if (currentPath === '/') return;
    
    // Get parent directory
    const pathParts = currentPath.split('/').filter(Boolean);
    pathParts.pop();
    const parentPath = pathParts.length === 0 ? '/' : '/' + pathParts.join('/');
    
    setCurrentPath(parentPath);
  };

  const handleRefreshClick = () => {
    fetchFiles(currentPath);
  };

  const handleBreadcrumbClick = (path) => {
    setCurrentPath(path);
  };

  const handleShareClick = async (file) => {
    setSharingLoading(true);
    setSharingError(null);
    setSharingLink(null);

    try {
      const linkInfo = await synologyService.createSharingLink(file.path);
      setSharingLink(linkInfo);
    } catch (err) {
      console.error('Error creating sharing link:', err);
      setSharingError('Failed to create sharing link. Please try again.');
    } finally {
      setSharingLoading(false);
    }
  };

  // Generate breadcrumbs from current path
  const generateBreadcrumbs = () => {
    const pathParts = currentPath.split('/').filter(Boolean);
    
    return (
      <Breadcrumbs aria-label="breadcrumb">
        <Link 
          color="inherit" 
          href="#" 
          onClick={(e) => {
            e.preventDefault();
            handleBreadcrumbClick('/');
          }}
        >
          Root
        </Link>
        
        {pathParts.map((part, index) => {
          const path = '/' + pathParts.slice(0, index + 1).join('/');
          const isLast = index === pathParts.length - 1;
          
          return isLast ? (
            <Typography color="text.primary" key={path}>
              {part}
            </Typography>
          ) : (
            <Link
              color="inherit"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleBreadcrumbClick(path);
              }}
              key={path}
            >
              {part}
            </Link>
          );
        })}
      </Breadcrumbs>
    );
  };

  if (loadingConfig) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!configStatus) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Synology File Browser
          </Typography>
          <Alert severity="warning">
            Synology is not configured yet. Please go to the Synology Setup page to configure your connection.
          </Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Synology File Browser
        </Typography>
        
        <Paper sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ flexGrow: 1 }}>
              {generateBreadcrumbs()}
            </Box>
            <Box>
              <Tooltip title="Go up">
                <IconButton 
                  onClick={handleUpClick} 
                  disabled={currentPath === '/'}
                  size="small"
                >
                  <UpIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton 
                  onClick={handleRefreshClick}
                  size="small"
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {sharingLink && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Sharing link created: <Link href={sharingLink.url} target="_blank">{sharingLink.url}</Link>
            </Alert>
          )}
          
          {sharingError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {sharingError}
            </Alert>
          )}
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List>
              {files.length === 0 ? (
                <ListItem>
                  <ListItemText primary="No files found in this directory" />
                </ListItem>
              ) : (
                files.map((file) => (
                  <React.Fragment key={file.path}>
                    <ListItem
                      disablePadding
                      secondaryAction={
                        <Box>
                          {!file.isdir && (
                            <Tooltip title="Download">
                              <IconButton 
                                edge="end" 
                                aria-label="download"
                                href={synologyService.getDownloadUrl(file.path)}
                                download
                              >
                                <DownloadIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="Share">
                            <IconButton 
                              edge="end" 
                              aria-label="share"
                              onClick={() => handleShareClick(file)}
                              disabled={sharingLoading}
                            >
                              {sharingLoading ? <CircularProgress size={24} /> : <ShareIcon />}
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                    >
                      <ListItemButton onClick={() => handleFileClick(file)}>
                        <ListItemIcon>
                          {file.isdir ? <FolderIcon /> : <FileIcon />}
                        </ListItemIcon>
                        <ListItemText 
                          primary={file.name} 
                          secondary={file.isdir ? 'Folder' : `File - ${file.size} bytes`} 
                        />
                      </ListItemButton>
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))
              )}
            </List>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default SynologyFileBrowser;