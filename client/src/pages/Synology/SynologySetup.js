import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip,
  FormControlLabel,
  Switch
} from '@mui/material';
import synologyService from '../../services/synologyService';

const SynologySetup = () => {
  const [formData, setFormData] = useState({
    host: '',
    port: '',
    username: '',
    password: '',
    secure: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  const { host, port, username, password, secure } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await synologyService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setFormData({ ...formData, [e.target.name]: value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await synologyService.saveConfig(formData);
      setSuccess(true);
      
      // Refresh the config status
      const config = await synologyService.getConfig();
      setConfigStatus(config);
      
      // Clear the form
      setFormData({
        host: '',
        port: '',
        username: '',
        password: '',
        secure: true
      });
    } catch (err) {
      setError('Failed to save Synology configuration. Please try again.');
      console.error('Error saving Synology configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Synology Setup
        </Typography>
        
        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Synology is configured with host: {configStatus.host}:{configStatus.port}
                    <br />
                    Username: {configStatus.username}
                    <br />
                    Secure connection: {configStatus.secure ? 'Yes' : 'No'}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Synology integration is not configured yet. Please provide your Synology credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Typography variant="body1" paragraph>
            To integrate with Synology, you need to provide your Synology DiskStation credentials.
          </Typography>
          
          <Typography variant="body1" paragraph>
            Enter the following information:
          </Typography>
          
          <ul>
            <li>
              <Typography variant="body1" paragraph>
                Host: The hostname or IP address of your Synology DiskStation
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Port: The port number (usually 5000 for HTTP or 5001 for HTTPS)
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Username: Your Synology account username
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Password: Your Synology account password
              </Typography>
            </li>
          </ul>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Synology configuration saved successfully!
            </Alert>
          )}
          
          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="host"
              label="Host"
              name="host"
              value={host}
              onChange={onChange}
              autoFocus
              placeholder="e.g., diskstation.local or *************"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="port"
              label="Port"
              name="port"
              value={port}
              onChange={onChange}
              placeholder="e.g., 5000 for HTTP or 5001 for HTTPS"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              value={username}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              value={password}
              onChange={onChange}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={secure}
                  onChange={onChange}
                  name="secure"
                  color="primary"
                />
              }
              label="Use secure connection (HTTPS)"
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default SynologySetup;