import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Container, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Paper,
  Breadcrumbs,
  Link
} from '@mui/material';
import { 
  Search as SearchIcon,
  Folder as FolderIcon,
  Description as FileIcon,
  Image as ImageIcon,
  Movie as VideoIcon,
  AudioFile as AudioIcon,
  PictureAsPdf as PdfIcon,
  TableChart as SpreadsheetIcon,
  Slideshow as PresentationIcon,
  Code as CodeIcon,
  InsertDriveFile as GenericFileIcon,
  ArrowBack as BackIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import moment from 'moment';

const DriveFilesPage = () => {
  const { user } = useAuth();
  const [files, setFiles] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searching, setSearching] = useState(false);

  // Fetch files from Google Drive
  useEffect(() => {
    const fetchFiles = async () => {
      try {
        setLoading(true);
        const res = await axios.get('/api/google/drive/files');
        setFiles(res.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching files:', err);
        setError('Failed to load files from Google Drive');
        setLoading(false);
      }
    };

    fetchFiles();
  }, []);

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle search submission
  const handleSearch = async (event) => {
    event.preventDefault();
    
    if (!searchTerm.trim()) {
      return;
    }
    
    try {
      setSearching(true);
      const res = await axios.get(`/api/google/drive/search?q=${encodeURIComponent(searchTerm)}`);
      setFiles(res.data);
      setSearching(false);
    } catch (err) {
      console.error('Error searching files:', err);
      setError('Failed to search Google Drive');
      setSearching(false);
    }
  };

  // Reset search and fetch all files
  const handleResetSearch = async () => {
    setSearchTerm('');
    try {
      setLoading(true);
      const res = await axios.get('/api/google/drive/files');
      setFiles(res.data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching files:', err);
      setError('Failed to load files from Google Drive');
      setLoading(false);
    }
  };

  // Get appropriate icon based on file mime type
  const getFileIcon = (mimeType) => {
    if (mimeType.includes('folder')) return <FolderIcon />;
    if (mimeType.includes('image')) return <ImageIcon />;
    if (mimeType.includes('video')) return <VideoIcon />;
    if (mimeType.includes('audio')) return <AudioIcon />;
    if (mimeType.includes('pdf')) return <PdfIcon />;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return <SpreadsheetIcon />;
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return <PresentationIcon />;
    if (mimeType.includes('document') || mimeType.includes('word')) return <FileIcon />;
    if (mimeType.includes('text') || mimeType.includes('code')) return <CodeIcon />;
    return <GenericFileIcon />;
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Google Drive Files
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Access and search your Google Drive files
        </Typography>
      </Box>

      {/* Search */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <form onSubmit={handleSearch}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs>
              <TextField
                fullWidth
                placeholder="Search files..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item>
              <Button 
                variant="contained" 
                color="primary" 
                type="submit"
                disabled={searching || !searchTerm.trim()}
              >
                {searching ? <CircularProgress size={24} /> : 'Search'}
              </Button>
            </Grid>
            {searchTerm && (
              <Grid item>
                <Button 
                  variant="outlined" 
                  onClick={handleResetSearch}
                  startIcon={<BackIcon />}
                >
                  Reset
                </Button>
              </Grid>
            )}
          </Grid>
        </form>
      </Paper>

      {/* Files list */}
      <Paper sx={{ p: 2 }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" component="h2">
            {searchTerm ? 'Search Results' : 'Recent Files'}
          </Typography>
        </Box>
        <Divider sx={{ mb: 2 }} />
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">{error}</Alert>
        ) : files.length === 0 ? (
          <Alert severity="info">
            {searchTerm ? 'No files found matching your search.' : 'No files found in your Google Drive.'}
          </Alert>
        ) : (
          <List>
            {files.map((file) => (
              <ListItem 
                key={file.id}
                disablePadding
                divider
              >
                <ListItemButton
                  component="a"
                  href={file.webViewLink}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ListItemIcon>
                    {getFileIcon(file.mimeType)}
                  </ListItemIcon>
                  <ListItemText 
                    primary={file.name} 
                    secondary={
                      <>
                        <Typography component="span" variant="body2" color="text.secondary">
                          {file.mimeType.includes('folder') ? 'Folder' : file.mimeType.split('/')[1]}
                        </Typography>
                        {' • '}
                        <Typography component="span" variant="body2" color="text.secondary">
                          Modified: {moment(file.modifiedTime).format('MMM D, YYYY')}
                        </Typography>
                      </>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </Paper>
    </Container>
  );
};

export default DriveFilesPage;