import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuth } from './context/AuthContext';

// Layout components
import Layout from './components/Layout';

// Page components
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import ShortcutsPage from './pages/ShortcutsPage';
import DriveFilesPage from './pages/DriveFilesPage';
import AdminUsersPage from './pages/AdminUsersPage';
import AdminRolesPage from './pages/AdminRolesPage';
import AdminShortcutsPage from './pages/AdminShortcutsPage';
import NotFoundPage from './pages/NotFoundPage';

// Protected route component
const ProtectedRoute = ({ children, requiredRoles = [] }) => {
  const { user, loading } = useAuth();

  // Show loading indicator while checking authentication
  if (loading) {
    return <Box sx={{ p: 3 }}>Loading...</Box>;
  }

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/login" />;
  }

  // If roles are required, check if user has at least one of them
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => user.roles.includes(role));
    if (!hasRequiredRole && !user.roles.includes('admin')) {
      return <Navigate to="/dashboard" />;
    }
  }

  return children;
};

function App() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        {/* Public routes */}
        <Route index element={<HomePage />} />
        <Route path="login" element={<LoginPage />} />
        
        {/* Protected routes */}
        <Route 
          path="dashboard" 
          element={
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="shortcuts" 
          element={
            <ProtectedRoute>
              <ShortcutsPage />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="drive" 
          element={
            <ProtectedRoute>
              <DriveFilesPage />
            </ProtectedRoute>
          } 
        />
        
        {/* Admin routes */}
        <Route 
          path="admin/users" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminUsersPage />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="admin/roles" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminRolesPage />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="admin/shortcuts" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminShortcutsPage />
            </ProtectedRoute>
          } 
        />
        
        {/* 404 route */}
        <Route path="*" element={<NotFoundPage />} />
      </Route>
    </Routes>
  );
}

export default App;