import { createTheme } from '@mui/material/styles';

// CSF Branding Colors
const csfBlue = {
  main: '#1a4b8c', // CSF Primary Blue
  light: '#4776bc',
  dark: '#00255e',
  contrastText: '#fff',
};

const csfGold = {
  main: '#e6b800', // CSF Gold/Yellow
  light: '#ffea4d',
  dark: '#b08800',
  contrastText: '#000',
};

// Create a theme instance with CSF branding
const theme = createTheme({
  palette: {
    primary: csfBlue,
    secondary: csfGold,
    background: {
      default: '#f8f9fa',
      paper: '#fff',
    },
  },
  typography: {
    fontFamily: [
      'Georgia',
      'Roboto',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
      color: csfBlue.main,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      color: csfBlue.main,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
      color: csfBlue.main,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      color: csfBlue.main,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      color: csfBlue.main,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      color: csfBlue.main,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        },
      },
    },
  },
});

export default theme;
