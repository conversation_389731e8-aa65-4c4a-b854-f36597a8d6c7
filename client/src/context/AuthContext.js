import React, { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';

// Create context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load user on initial render
  useEffect(() => {
    const loadUser = async () => {
      try {
        const res = await axios.get('/api/auth/current');
        setUser(res.data);
        setLoading(false);
      } catch (err) {
        console.error('Error loading user:', err);
        setUser(null);
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  // Login with Google
  const loginWithGoogle = () => {
    window.location.href = '/api/auth/google';
  };

  // Logout
  const logout = async () => {
    try {
      await axios.get('/api/auth/logout');
      setUser(null);
    } catch (err) {
      console.error('Error logging out:', err);
      setError('Error logging out');
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    if (!user) return false;
    return user.roles.includes(role);
  };

  // Check if user has admin role
  const isAdmin = () => {
    return hasRole('admin');
  };

  // Value object to be provided to consumers
  const value = {
    user,
    loading,
    error,
    loginWithGoogle,
    logout,
    hasRole,
    isAdmin
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};