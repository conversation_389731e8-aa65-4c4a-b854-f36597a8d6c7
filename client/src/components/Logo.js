import React from 'react';
import { Box, Typography } from '@mui/material';
import { colors } from '../styles/theme';
import csfLogo from '../assets/csf-logo.svg';

/**
 * CSF Logo Component
 * Displays the CSF logo with optional text
 * @param {Object} props - Component props
 * @param {string} props.variant - Logo variant (full, icon, text)
 * @param {number} props.height - Logo height
 * @param {boolean} props.showText - Whether to show text with the logo
 * @param {Object} props.sx - Additional styles
 * @returns {JSX.Element} - Logo component
 */
const Logo = ({ 
  variant = 'full', 
  height = 40, 
  showText = true,
  sx = {} 
}) => {
  // Calculate width based on height and variant
  const getWidth = () => {
    if (variant === 'icon') return height;
    if (variant === 'text') return 'auto';
    return height * 3.5; // full logo has 3.5:1 aspect ratio
  };

  // SVG logo (using imported SVG file)
  const renderLogo = () => {
    if (variant === 'text') return null;

    const width = variant === 'icon' ? height : height * 2;

    // Use the imported SVG file for the full logo
    if (variant === 'full' || variant === 'icon') {
      return (
        <Box
          component="img"
          src={csfLogo}
          alt="CSF Logo"
          width={width}
          height={height}
          sx={{ mr: variant === 'full' && showText ? 1 : 0 }}
        />
      );
    }

    // Fallback to the inline SVG for other variants
    return (
      <Box
        component="svg"
        width={width}
        height={height}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        sx={{ mr: variant === 'full' && showText ? 1 : 0 }}
      >
        {/* Cross symbol */}
        <path
          d="M50 10 L50 90 M20 50 L80 50"
          stroke={colors.primary.main}
          strokeWidth="12"
          strokeLinecap="round"
        />
        {/* Circle around the cross */}
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke={colors.primary.main}
          strokeWidth="6"
          fill="none"
        />
      </Box>
    );
  };

  // Text part of the logo
  const renderText = () => {
    if (!showText && variant !== 'text') return null;

    return (
      <Box>
        <Typography
          variant="h6"
          component="span"
          sx={{
            fontWeight: 700,
            color: colors.primary.main,
            letterSpacing: '0.02em',
            lineHeight: 1.2,
            fontSize: height * 0.4,
            display: 'block',
          }}
        >
          CHRISTIAN STUDENT
        </Typography>
        <Typography
          variant="h6"
          component="span"
          sx={{
            fontWeight: 700,
            color: colors.secondary.main,
            letterSpacing: '0.02em',
            lineHeight: 1.2,
            fontSize: height * 0.4,
            display: 'block',
          }}
        >
          FELLOWSHIP
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        width: getWidth(),
        height,
        ...sx,
      }}
    >
      {renderLogo()}
      {renderText()}
    </Box>
  );
};

export default Logo;
