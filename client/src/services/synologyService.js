import axios from 'axios';

/**
 * Synology API Service
 * Handles client-side API calls to the Synology endpoints
 */
const synologyService = {
  /**
   * Save Synology configuration
   * @param {Object} config Configuration object with host, port, username, password, and secure
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/synology/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Synology configuration:', error);
      throw error;
    }
  },

  /**
   * Get Synology configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/synology/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Synology configuration:', error);
      throw error;
    }
  },

  /**
   * List files in a directory
   * @param {string} path Path to the directory
   * @param {Object} options Additional options
   * @returns {Promise<Array>} List of files
   */
  listFiles: async (path, options = {}) => {
    try {
      const params = { path, ...options };
      const response = await axios.get('/api/synology/files', { params });
      return response.data;
    } catch (error) {
      console.error('Error listing files from Synology:', error);
      throw error;
    }
  },

  /**
   * Get download URL for a file
   * @param {string} path Path to the file
   * @returns {string} Download URL
   */
  getDownloadUrl: (path) => {
    const params = new URLSearchParams({ path });
    return `/api/synology/download?${params.toString()}`;
  },

  /**
   * Create a sharing link
   * @param {string} path Path to the file or folder
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Sharing link info
   */
  createSharingLink: async (path, options = {}) => {
    try {
      const response = await axios.post('/api/synology/share', { path, ...options });
      return response.data;
    } catch (error) {
      console.error('Error creating sharing link on Synology:', error);
      throw error;
    }
  }
};

export default synologyService;