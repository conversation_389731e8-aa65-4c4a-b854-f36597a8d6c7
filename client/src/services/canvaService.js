import axios from 'axios';

/**
 * Canva API Service
 * Handles client-side API calls to the Canva graphic design service endpoints
 */
const canvaService = {
  /**
   * Get all Canva designs
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of designs
   */
  getDesigns: async (params = {}) => {
    try {
      const response = await axios.get('/api/canva/designs', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva designs:', error);
      throw error;
    }
  },

  /**
   * Get Canva design details
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Design details
   */
  getDesign: async (designId, params = {}) => {
    try {
      const response = await axios.get(`/api/canva/designs/${designId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId}:`, error);
      throw error;
    }
  },

  /**
   * Get Canva templates
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of templates
   */
  getTemplates: async (params = {}) => {
    try {
      const response = await axios.get(`/api/canva/templates`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva templates:`, error);
      throw error;
    }
  },

  /**
   * Get Canva design assets
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of assets
   */
  getDesignAssets: async (designId, params = {}) => {
    try {
      const response = await axios.get(`/api/canva/designs/${designId}/assets`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId} assets:`, error);
      throw error;
    }
  },

  /**
   * Get Canva design files
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of files
   */
  getDesignFiles: async (designId, params = {}) => {
    try {
      const response = await axios.get(`/api/canva/designs/${designId}/files`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId} files:`, error);
      throw error;
    }
  },

  /**
   * Get Canva file details
   * @param {string} fileId File ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} File details
   */
  getFile: async (fileId, params = {}) => {
    try {
      const response = await axios.get(`/api/canva/files/${fileId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva file ${fileId}:`, error);
      throw error;
    }
  },

  /**
   * Get Canva users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axios.get('/api/canva/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva users:', error);
      throw error;
    }
  },

  /**
   * Get Canva user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  getUser: async (userId, params = {}) => {
    try {
      const response = await axios.get(`/api/canva/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Save Canva configuration
   * @param {Object} config Configuration object with domain and apiKey
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/canva/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Canva configuration:', error);
      throw error;
    }
  },

  /**
   * Get Canva configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/canva/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Canva configuration:', error);
      throw error;
    }
  }
};

export default canvaService;