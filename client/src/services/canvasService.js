import axios from 'axios';

/**
 * Canvas LMS API Service
 * Handles client-side API calls to the Canvas LMS endpoints
 */
const canvasService = {
  /**
   * Get all Canvas courses
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of courses
   */
  getCourses: async (params = {}) => {
    try {
      const response = await axios.get('/api/canvas/courses', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canvas courses:', error);
      throw error;
    }
  },

  /**
   * Get Canvas course details
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Course details
   */
  getCourse: async (courseId, params = {}) => {
    try {
      const response = await axios.get(`/api/canvas/courses/${courseId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId}:`, error);
      throw error;
    }
  },

  /**
   * Get Canvas course modules
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of modules
   */
  getCourseModules: async (courseId, params = {}) => {
    try {
      const response = await axios.get(`/api/canvas/courses/${courseId}/modules`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId} modules:`, error);
      throw error;
    }
  },

  /**
   * Get Canvas course assignments
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of assignments
   */
  getCourseAssignments: async (courseId, params = {}) => {
    try {
      const response = await axios.get(`/api/canvas/courses/${courseId}/assignments`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId} assignments:`, error);
      throw error;
    }
  },

  /**
   * Get Canvas course files
   * @param {string} courseId Course ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of files
   */
  getCourseFiles: async (courseId, params = {}) => {
    try {
      const response = await axios.get(`/api/canvas/courses/${courseId}/files`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas course ${courseId} files:`, error);
      throw error;
    }
  },

  /**
   * Get Canvas file details
   * @param {string} fileId File ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} File details
   */
  getFile: async (fileId, params = {}) => {
    try {
      const response = await axios.get(`/api/canvas/files/${fileId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas file ${fileId}:`, error);
      throw error;
    }
  },

  /**
   * Get Canvas users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axios.get('/api/canvas/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canvas users:', error);
      throw error;
    }
  },

  /**
   * Get Canvas user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  getUser: async (userId, params = {}) => {
    try {
      const response = await axios.get(`/api/canvas/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canvas user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Save Canvas configuration
   * @param {Object} config Configuration object with domain and apiKey
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/canvas/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Canvas configuration:', error);
      throw error;
    }
  },

  /**
   * Get Canvas configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/canvas/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Canvas configuration:', error);
      throw error;
    }
  }
};

export default canvasService;