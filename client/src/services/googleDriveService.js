import axios from 'axios';

/**
 * Google Drive API Service
 * Handles client-side API calls to the Google Drive endpoints
 */
const googleDriveService = {
  /**
   * Save Google Drive configuration
   * @param {Object} config Configuration object with clientId, clientSecret, redirectUri, and tokenPath
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/google-drive/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Google Drive configuration:', error);
      throw error;
    }
  },

  /**
   * Get Google Drive configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/google-drive/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Google Drive configuration:', error);
      throw error;
    }
  },

  /**
   * Get authentication URL for OAuth2 flow
   * @returns {Promise<string>} Authentication URL
   */
  getAuthUrl: async () => {
    try {
      const response = await axios.get('/api/google-drive/auth-url');
      return response.data.authUrl;
    } catch (error) {
      console.error('Error getting Google Drive auth URL:', error);
      throw error;
    }
  },

  /**
   * List files in Google Drive
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of files
   */
  listFiles: async (options = {}) => {
    try {
      const response = await axios.get('/api/google-drive/files', { params: options });
      return response.data;
    } catch (error) {
      console.error('Error listing files from Google Drive:', error);
      throw error;
    }
  },

  /**
   * Search files in Google Drive
   * @param {string} query Search query
   * @param {Object} options Additional options
   * @returns {Promise<Array>} List of files
   */
  searchFiles: async (query, options = {}) => {
    try {
      const params = { query, ...options };
      const response = await axios.get('/api/google-drive/search', { params });
      return response.data;
    } catch (error) {
      console.error('Error searching files in Google Drive:', error);
      throw error;
    }
  },

  /**
   * Get file metadata
   * @param {string} fileId File ID
   * @returns {Promise<Object>} File metadata
   */
  getFile: async (fileId) => {
    try {
      const response = await axios.get(`/api/google-drive/files/${fileId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting file from Google Drive:', error);
      throw error;
    }
  },

  /**
   * Get embedded viewer URL for a file
   * @param {string} fileId File ID
   * @returns {Promise<string>} Embedded viewer URL
   */
  getEmbeddedViewerUrl: async (fileId) => {
    try {
      const response = await axios.get(`/api/google-drive/viewer/${fileId}`);
      return response.data.viewerUrl;
    } catch (error) {
      console.error('Error getting embedded viewer URL:', error);
      throw error;
    }
  }
};

export default googleDriveService;