const GoogleStrategy = require('passport-google-oauth20').Strategy;
const mongoose = require('mongoose');
const User = require('../models/User');

module.exports = function(passport) {
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: process.env.GOOGLE_CALLBACK_URL,
        proxy: true
      },
      async (accessToken, refreshToken, profile, done) => {
        // Extract email domain to check if it's allowed
        const email = profile.emails[0].value;
        const domain = email.split('@')[1];
        const allowedDomains = process.env.ALLOWED_DOMAINS.split(',');
        
        if (!allowedDomains.includes(domain)) {
          return done(null, false, { message: 'Domain not allowed' });
        }
        
        try {
          // Check if user already exists
          let user = await User.findOne({ googleId: profile.id });
          
          if (user) {
            // Update user's tokens
            user.googleAccessToken = accessToken;
            user.googleRefreshToken = refreshToken;
            await user.save();
            return done(null, user);
          }
          
          // If not, create new user
          const newUser = new User({
            googleId: profile.id,
            name: profile.displayName,
            email: profile.emails[0].value,
            avatar: profile.photos[0].value,
            googleAccessToken: accessToken,
            googleRefreshToken: refreshToken,
            roles: ['user'] // Default role
          });
          
          await newUser.save();
          return done(null, newUser);
        } catch (err) {
          console.error(err);
          return done(err);
        }
      }
    )
  );
  
  // Serialize user for the session
  passport.serializeUser((user, done) => {
    done(null, user.id);
  });
  
  // Deserialize user from the session
  passport.deserializeUser(async (id, done) => {
    try {
      const user = await User.findById(id).select('-googleAccessToken -googleRefreshToken');
      done(null, user);
    } catch (err) {
      done(err);
    }
  });
};