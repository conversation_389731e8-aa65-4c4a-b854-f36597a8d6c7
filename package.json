{"name": "csfportal", "version": "1.0.0", "description": "Intranet platform for church staff", "main": "server.js", "scripts": {"start": "node server.js", "server": "nodemon server.js", "client": "npm start --prefix client", "dev": "concurrently \"npm run server\" \"npm run client\"", "test": "jest", "test:watch": "jest --watch", "client-install": "npm install --prefix client", "heroku-postbuild": "NPM_CONFIG_PRODUCTION=false npm install --prefix client && npm run build --prefix client"}, "private": true, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "connect-mongo": "^4.6.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "express-validator": "^6.14.2", "googleapis": "^110.0.0", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.0", "mongoose": "^6.8.3", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0"}, "devDependencies": {"concurrently": "^7.6.0", "jest": "^29.5.0", "mongodb-memory-server": "^8.12.2", "nodemon": "^2.0.20", "supertest": "^6.3.3"}, "engines": {"node": "16.x"}}