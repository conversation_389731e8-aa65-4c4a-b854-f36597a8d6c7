# Church Staff Portal

A secure intranet platform for church staff to access important information and services. This platform integrates with Google services, provides role-based access control, and includes a customizable shortcuts page.

## Features

- **Google Authentication**: Secure login with Google accounts, restricted to specific email domains
- **Role-Based Access Control**: Different permission levels for different staff members
- **Shortcuts Page**: Quick access to important resources with category tagging and search
- **Google Drive Integration**: Browse and search files from Google Drive
- **Google Admin Integration**: Manage users and groups (admin only)
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Backend**: Node.js, Express
- **Frontend**: React, Material-UI
- **Authentication**: Passport.js with Google OAuth 2.0
- **Database**: MongoDB
- **API Integration**: Google Drive API, Google Admin SDK

## Prerequisites

- Node.js (v16 or higher)
- MongoDB
- Google Cloud Platform account with OAuth 2.0 credentials
- Google Workspace (formerly G Suite) account with Admin privileges

## Setup Instructions

### 1. Clone the repository

```bash
git clone https://github.com/yourusername/csfportal.git
cd csfportal
```

### 2. Install dependencies

```bash
# Install backend dependencies
npm install

# Install frontend dependencies
npm run client-install
```

### 3. Configure environment variables

Create a `.env` file in the root directory with the following variables:

```
NODE_ENV=development
PORT=5000
MONGO_URI=mongodb://localhost:27017/csfportal
SESSION_SECRET=your_session_secret_change_this_in_production

# Google OAuth credentials
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback

# Google API credentials for Drive and Admin SDK
GOOGLE_API_KEY=your_google_api_key

# Allowed domains for Google login (comma-separated)
ALLOWED_DOMAINS=yourchurch.org
```

### 4. Set up Google Cloud Platform

1. Create a new project in Google Cloud Platform
2. Enable the following APIs:
   - Google Drive API
   - Admin SDK
3. Create OAuth 2.0 credentials
   - Set authorized redirect URI to `http://localhost:5000/api/auth/google/callback` for development
4. Create API key for Google services

### 5. Initialize the database

Start the application and navigate to the following endpoints to initialize default data:

- `/api/roles/init` - Creates default roles (admin, user)
- `/api/shortcuts/init` - Creates default shortcuts

### 6. Run the application

```bash
# Development mode (runs both backend and frontend)
npm run dev

# Run backend only
npm run server

# Run frontend only
npm run client
```

## Security Considerations

- **HTTPS**: In production, always use HTTPS to encrypt data in transit
- **Domain Restriction**: Only allow users from specific email domains to log in
- **Role-Based Access**: Implement proper authorization checks for all routes
- **Input Validation**: Validate all user inputs to prevent injection attacks
- **API Key Security**: Store API keys securely and use environment variables
- **Session Management**: Use secure, HTTP-only cookies for sessions
- **Regular Updates**: Keep all dependencies updated to patch security vulnerabilities

## Deployment

### Heroku Deployment

```bash
# Login to Heroku
heroku login

# Create a new Heroku app
heroku create

# Add MongoDB add-on
heroku addons:create mongodb

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set SESSION_SECRET=your_session_secret
heroku config:set GOOGLE_CLIENT_ID=your_google_client_id
heroku config:set GOOGLE_CLIENT_SECRET=your_google_client_secret
heroku config:set GOOGLE_CALLBACK_URL=https://your-app.herokuapp.com/api/auth/google/callback
heroku config:set GOOGLE_API_KEY=your_google_api_key
heroku config:set ALLOWED_DOMAINS=yourchurch.org

# Push to Heroku
git push heroku main
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Google APIs Node.js Client](https://github.com/googleapis/google-api-nodejs-client)
- [Passport.js](http://www.passportjs.org/)
- [Material-UI](https://mui.com/)
- [React](https://reactjs.org/)
- [Express](https://expressjs.com/)
- [MongoDB](https://www.mongodb.com/)