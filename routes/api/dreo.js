const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const dreoController = require('../../server/controllers/dreoController');

// @route   GET api/dreo/devices
// @desc    Get all Dreo devices
// @access  Private
router.get('/devices', auth, dreoController.getDevices);

// @route   GET api/dreo/devices/:id
// @desc    Get Dreo device details
// @access  Private
router.get('/devices/:id', auth, dreoController.getDeviceDetails);

// @route   POST api/dreo/devices/:id/power
// @desc    Set device power state
// @access  Private
router.post('/devices/:id/power', auth, dreoController.setPower);

// @route   POST api/dreo/devices/:id/temperature
// @desc    Set device temperature
// @access  Private
router.post('/devices/:id/temperature', auth, dreoController.setTemperature);

// @route   POST api/dreo/devices/:id/fan-speed
// @desc    Set device fan speed
// @access  Private
router.post('/devices/:id/fan-speed', auth, dreoController.setFanSpeed);

// @route   POST api/dreo/devices/:id/mode
// @desc    Set device mode
// @access  Private
router.post('/devices/:id/mode', auth, dreoController.setMode);

// @route   POST api/dreo/config
// @desc    Save Dreo configuration
// @access  Private (admin only)
router.post('/config', auth, dreoController.saveConfig);

// @route   GET api/dreo/config
// @desc    Get Dreo configuration status
// @access  Private
router.get('/config', auth, dreoController.getConfig);

module.exports = router;