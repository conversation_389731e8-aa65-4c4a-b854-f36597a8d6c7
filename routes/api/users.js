const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const User = require('../../models/User');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/users
 * @desc    Get all users
 * @access  private
 */
router.get('/', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const users = await User.find().select('-googleAccessToken -googleRefreshToken');
    res.json(users);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-googleAccessToken -googleRefreshToken');
    
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    res.json(user);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/users/:id/roles
 * @desc    Update user roles
 * @access  private
 */
router.put(
  '/:id/roles',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('roles', 'Roles are required').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.params.id);
      
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }
      
      user.roles = req.body.roles;
      await user.save();
      
      res.json(user);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'User not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/users/:id/status
 * @desc    Activate or deactivate user
 * @access  private
 */
router.put(
  '/:id/status',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('isActive', 'isActive field is required').isBoolean()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.params.id);
      
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }
      
      user.isActive = req.body.isActive;
      await user.save();
      
      res.json(user);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'User not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

module.exports = router;