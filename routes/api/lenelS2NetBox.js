const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const lenelS2NetBoxController = require('../../server/controllers/lenelS2NetBoxController');

// @route   GET api/lenel-s2-netbox/access-points
// @desc    Get all Lenel S2 NetBox access points
// @access  Private
router.get('/access-points', auth, lenelS2NetBoxController.getAccessPoints);

// @route   GET api/lenel-s2-netbox/access-points/:id
// @desc    Get Lenel S2 NetBox access point details
// @access  Private
router.get('/access-points/:id', auth, lenelS2NetBoxController.getAccessPointDetails);

// @route   GET api/lenel-s2-netbox/cardholders
// @desc    Get all Lenel S2 NetBox cardholders
// @access  Private
router.get('/cardholders', auth, lenelS2NetBoxController.getCardholders);

// @route   GET api/lenel-s2-netbox/cardholders/:id
// @desc    Get Lenel S2 NetBox cardholder details
// @access  Private
router.get('/cardholders/:id', auth, lenelS2NetBoxController.getCardholderDetails);

// @route   GET api/lenel-s2-netbox/alarms
// @desc    Get all Lenel S2 NetBox alarms
// @access  Private
router.get('/alarms', auth, lenelS2NetBoxController.getAlarms);

// @route   POST api/lenel-s2-netbox/alarms/:id/acknowledge
// @desc    Acknowledge a Lenel S2 NetBox alarm
// @access  Private
router.post('/alarms/:id/acknowledge', auth, lenelS2NetBoxController.acknowledgeAlarm);

// @route   GET api/lenel-s2-netbox/events
// @desc    Get all Lenel S2 NetBox events
// @access  Private
router.get('/events', auth, lenelS2NetBoxController.getEvents);

// @route   GET api/lenel-s2-netbox/system/status
// @desc    Get Lenel S2 NetBox system status
// @access  Private
router.get('/system/status', auth, lenelS2NetBoxController.getSystemStatus);

// @route   POST api/lenel-s2-netbox/doors/:id/unlock
// @desc    Unlock a Lenel S2 NetBox door
// @access  Private
router.post('/doors/:id/unlock', auth, lenelS2NetBoxController.unlockDoor);

// @route   POST api/lenel-s2-netbox/doors/:id/lock
// @desc    Lock a Lenel S2 NetBox door
// @access  Private
router.post('/doors/:id/lock', auth, lenelS2NetBoxController.lockDoor);

// @route   POST api/lenel-s2-netbox/config
// @desc    Save Lenel S2 NetBox configuration
// @access  Private (admin only)
router.post('/config', auth, lenelS2NetBoxController.saveConfig);

// @route   GET api/lenel-s2-netbox/config
// @desc    Get Lenel S2 NetBox configuration status
// @access  Private
router.get('/config', auth, lenelS2NetBoxController.getConfig);

module.exports = router;