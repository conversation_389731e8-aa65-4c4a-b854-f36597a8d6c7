const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const Shortcut = require('../../models/Shortcut');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/shortcuts
 * @desc    Get all shortcuts
 * @access  public
 */
router.get('/', async (req, res) => {
  try {
    // Filter by category if provided
    const filter = {};
    if (req.query.category) {
      filter.categories = req.query.category;
    }
    
    // Filter by active status
    filter.isActive = true;
    
    // Get shortcuts
    const shortcuts = await Shortcut.find(filter).sort({ title: 1 });
    
    // Filter shortcuts based on user roles
    let filteredShortcuts = shortcuts;
    
    // If user is not authenticated, only return public shortcuts
    if (!req.isAuthenticated()) {
      filteredShortcuts = shortcuts.filter(shortcut => shortcut.isPublic);
    } else {
      // If user is authenticated, filter by required roles
      // Admin can see all shortcuts
      if (!req.user.roles.includes('admin')) {
        filteredShortcuts = shortcuts.filter(shortcut => 
          shortcut.isPublic || 
          shortcut.requiredRoles.some(role => req.user.roles.includes(role))
        );
      }
    }
    
    res.json(filteredShortcuts);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/shortcuts/search
 * @desc    Search shortcuts
 * @access  public
 */
router.get('/search', async (req, res) => {
  try {
    const searchTerm = req.query.q;
    
    if (!searchTerm) {
      return res.status(400).json({ msg: 'Search term is required' });
    }
    
    // Search shortcuts using text index
    const shortcuts = await Shortcut.find(
      { 
        $text: { $search: searchTerm },
        isActive: true
      },
      { 
        score: { $meta: 'textScore' } 
      }
    ).sort({ score: { $meta: 'textScore' } });
    
    // Filter shortcuts based on user roles (same as above)
    let filteredShortcuts = shortcuts;
    
    if (!req.isAuthenticated()) {
      filteredShortcuts = shortcuts.filter(shortcut => shortcut.isPublic);
    } else {
      if (!req.user.roles.includes('admin')) {
        filteredShortcuts = shortcuts.filter(shortcut => 
          shortcut.isPublic || 
          shortcut.requiredRoles.some(role => req.user.roles.includes(role))
        );
      }
    }
    
    res.json(filteredShortcuts);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/shortcuts/categories
 * @desc    Get all unique categories
 * @access  public
 */
router.get('/categories', async (req, res) => {
  try {
    const shortcuts = await Shortcut.find({ isActive: true });
    
    // Extract all categories and remove duplicates
    const categories = [...new Set(shortcuts.flatMap(shortcut => shortcut.categories))];
    
    res.json(categories.sort());
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/shortcuts/:id
 * @desc    Get shortcut by ID
 * @access  public
 */
router.get('/:id', async (req, res) => {
  try {
    const shortcut = await Shortcut.findById(req.params.id);
    
    if (!shortcut) {
      return res.status(404).json({ msg: 'Shortcut not found' });
    }
    
    // Check if user has access to this shortcut
    if (!shortcut.isPublic && 
        (!req.isAuthenticated() || 
         (!req.user.roles.includes('admin') && 
          !shortcut.requiredRoles.some(role => req.user.roles.includes(role))))) {
      return res.status(403).json({ msg: 'Access denied' });
    }
    
    res.json(shortcut);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Shortcut not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/shortcuts
 * @desc    Create a shortcut
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('title', 'Title is required').not().isEmpty(),
    check('url', 'URL is required').not().isEmpty(),
    check('categories', 'Categories must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      title, 
      description, 
      url, 
      icon, 
      categories, 
      instructions,
      requiredRoles,
      isPublic
    } = req.body;

    try {
      // Create new shortcut
      const shortcut = new Shortcut({
        title,
        description,
        url,
        icon: icon || 'link',
        categories: categories.length > 0 ? categories : ['General'],
        instructions,
        requiredRoles: requiredRoles || ['user'],
        isPublic: isPublic || false,
        createdBy: req.user.id
      });
      
      await shortcut.save();
      
      res.json(shortcut);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/shortcuts/:id
 * @desc    Update a shortcut
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('title', 'Title is required').not().isEmpty(),
    check('url', 'URL is required').not().isEmpty(),
    check('categories', 'Categories must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const shortcut = await Shortcut.findById(req.params.id);
      
      if (!shortcut) {
        return res.status(404).json({ msg: 'Shortcut not found' });
      }
      
      // Update shortcut fields
      const { 
        title, 
        description, 
        url, 
        icon, 
        categories, 
        instructions,
        requiredRoles,
        isPublic,
        isActive
      } = req.body;
      
      shortcut.title = title;
      shortcut.description = description;
      shortcut.url = url;
      shortcut.icon = icon || 'link';
      shortcut.categories = categories.length > 0 ? categories : ['General'];
      shortcut.instructions = instructions;
      shortcut.requiredRoles = requiredRoles || ['user'];
      shortcut.isPublic = isPublic || false;
      
      if (isActive !== undefined) {
        shortcut.isActive = isActive;
      }
      
      await shortcut.save();
      
      res.json(shortcut);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Shortcut not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/shortcuts/:id
 * @desc    Delete a shortcut
 * @access  private
 */
router.delete('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const shortcut = await Shortcut.findById(req.params.id);
    
    if (!shortcut) {
      return res.status(404).json({ msg: 'Shortcut not found' });
    }
    
    await shortcut.remove();
    
    res.json({ msg: 'Shortcut removed' });
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Shortcut not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/shortcuts/:id/click
 * @desc    Increment click count for a shortcut
 * @access  public
 */
router.post('/:id/click', async (req, res) => {
  try {
    const shortcut = await Shortcut.findById(req.params.id);
    
    if (!shortcut) {
      return res.status(404).json({ msg: 'Shortcut not found' });
    }
    
    // Check if user has access to this shortcut
    if (!shortcut.isPublic && 
        (!req.isAuthenticated() || 
         (!req.user.roles.includes('admin') && 
          !shortcut.requiredRoles.some(role => req.user.roles.includes(role))))) {
      return res.status(403).json({ msg: 'Access denied' });
    }
    
    await shortcut.incrementClickCount();
    
    res.json({ success: true });
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Shortcut not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/shortcuts/init
 * @desc    Initialize default shortcuts
 * @access  private
 */
router.post('/init', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    await Shortcut.createDefaultShortcuts();
    const shortcuts = await Shortcut.find();
    res.json(shortcuts);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;