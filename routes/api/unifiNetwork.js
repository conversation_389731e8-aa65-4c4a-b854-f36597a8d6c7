const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const unifiNetworkController = require('../../server/controllers/unifiNetworkController');

// @route   GET api/unifi-network/devices
// @desc    Get all UniFi Network devices
// @access  Private
router.get('/devices', auth, unifiNetworkController.getDevices);

// @route   GET api/unifi-network/devices/:id
// @desc    Get UniFi Network device details
// @access  Private
router.get('/devices/:id', auth, unifiNetworkController.getDeviceDetails);

// @route   GET api/unifi-network/clients
// @desc    Get all UniFi Network clients
// @access  Private
router.get('/clients', auth, unifiNetworkController.getClients);

// @route   GET api/unifi-network/clients/:id
// @desc    Get UniFi Network client details
// @access  Private
router.get('/clients/:id', auth, unifiNetworkController.getClientDetails);

// @route   POST api/unifi-network/clients/:id/block
// @desc    Block a UniFi Network client
// @access  Private
router.post('/clients/:id/block', auth, unifiNetworkController.blockClient);

// @route   POST api/unifi-network/clients/:id/unblock
// @desc    Unblock a UniFi Network client
// @access  Private
router.post('/clients/:id/unblock', auth, unifiNetworkController.unblockClient);

// @route   GET api/unifi-network/stats
// @desc    Get UniFi Network statistics
// @access  Private
router.get('/stats', auth, unifiNetworkController.getNetworkStats);

// @route   GET api/unifi-network/site
// @desc    Get UniFi Network site information
// @access  Private
router.get('/site', auth, unifiNetworkController.getSiteInfo);

// @route   POST api/unifi-network/config
// @desc    Save UniFi Network configuration
// @access  Private (admin only)
router.post('/config', auth, unifiNetworkController.saveConfig);

// @route   GET api/unifi-network/config
// @desc    Get UniFi Network configuration status
// @access  Private
router.get('/config', auth, unifiNetworkController.getConfig);

module.exports = router;