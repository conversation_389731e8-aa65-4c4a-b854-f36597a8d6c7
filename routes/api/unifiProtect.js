const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const unifiProtectController = require('../../server/controllers/unifiProtectController');

// @route   GET api/unifi-protect/cameras
// @desc    Get all UniFi Protect cameras
// @access  Private
router.get('/cameras', auth, unifiProtectController.getCameras);

// @route   GET api/unifi-protect/cameras/:id
// @desc    Get UniFi Protect camera details
// @access  Private
router.get('/cameras/:id', auth, unifiProtectController.getCameraDetails);

// @route   GET api/unifi-protect/cameras/:id/snapshot
// @desc    Get UniFi Protect camera snapshot
// @access  Private
router.get('/cameras/:id/snapshot', auth, unifiProtectController.getCameraSnapshot);

// @route   GET api/unifi-protect/events
// @desc    Get UniFi Protect events
// @access  Private
router.get('/events', auth, unifiProtectController.getEvents);

// @route   GET api/unifi-protect/system
// @desc    Get UniFi Protect system status
// @access  Private
router.get('/system', auth, unifiProtectController.getSystemStatus);

// @route   POST api/unifi-protect/config
// @desc    Save UniFi Protect configuration
// @access  Private (admin only)
router.post('/config', auth, unifiProtectController.saveConfig);

// @route   GET api/unifi-protect/config
// @desc    Get UniFi Protect configuration status
// @access  Private
router.get('/config', auth, unifiProtectController.getConfig);

module.exports = router;