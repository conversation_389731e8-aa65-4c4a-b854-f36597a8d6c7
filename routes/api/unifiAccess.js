const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const unifiAccessController = require('../../server/controllers/unifiAccessController');

// @route   GET api/unifi-access/doors
// @desc    Get all UniFi Access doors
// @access  Private
router.get('/doors', auth, unifiAccessController.getDoors);

// @route   GET api/unifi-access/doors/:id
// @desc    Get UniFi Access door details
// @access  Private
router.get('/doors/:id', auth, unifiAccessController.getDoorDetails);

// @route   POST api/unifi-access/doors/:id/unlock
// @desc    Unlock a door
// @access  Private
router.post('/doors/:id/unlock', auth, unifiAccessController.unlockDoor);

// @route   POST api/unifi-access/doors/:id/lock
// @desc    Lock a door
// @access  Private
router.post('/doors/:id/lock', auth, unifiAccessController.lockDoor);

// @route   GET api/unifi-access/access-points
// @desc    Get all UniFi Access access points
// @access  Private
router.get('/access-points', auth, unifiAccessController.getAccessPoints);

// @route   GET api/unifi-access/users
// @desc    Get all UniFi Access users
// @access  Private
router.get('/users', auth, unifiAccessController.getUsers);

// @route   GET api/unifi-access/events
// @desc    Get UniFi Access events
// @access  Private
router.get('/events', auth, unifiAccessController.getEvents);

// @route   GET api/unifi-access/system
// @desc    Get UniFi Access system status
// @access  Private
router.get('/system', auth, unifiAccessController.getSystemStatus);

// @route   POST api/unifi-access/config
// @desc    Save UniFi Access configuration
// @access  Private (admin only)
router.post('/config', auth, unifiAccessController.saveConfig);

// @route   GET api/unifi-access/config
// @desc    Get UniFi Access configuration status
// @access  Private
router.get('/config', auth, unifiAccessController.getConfig);

module.exports = router;