const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const auth = require('../../middleware/auth');
const canvasController = require('../../server/controllers/canvasController');

// @route   GET api/canvas/courses
// @desc    Get all Canvas courses
// @access  Private
router.get('/courses', auth, canvasController.getCourses);

// @route   GET api/canvas/courses/:id
// @desc    Get Canvas course details
// @access  Private
router.get('/courses/:id', auth, canvasController.getCourse);

// @route   GET api/canvas/courses/:id/modules
// @desc    Get Canvas course modules
// @access  Private
router.get('/courses/:id/modules', auth, canvasController.getCourseModules);

// @route   GET api/canvas/courses/:id/assignments
// @desc    Get Canvas course assignments
// @access  Private
router.get('/courses/:id/assignments', auth, canvasController.getCourseAssignments);

// @route   GET api/canvas/courses/:id/files
// @desc    Get Canvas course files
// @access  Private
router.get('/courses/:id/files', auth, canvasController.getCourseFiles);

// @route   GET api/canvas/files/:id
// @desc    Get Canvas file details
// @access  Private
router.get('/files/:id', auth, canvasController.getFile);

// @route   GET api/canvas/users
// @desc    Get Canvas users
// @access  Private
router.get('/users', auth, canvasController.getUsers);

// @route   GET api/canvas/users/:id
// @desc    Get Canvas user details
// @access  Private
router.get('/users/:id', auth, canvasController.getUser);

// @route   POST api/canvas/config
// @desc    Save Canvas configuration
// @access  Private (admin only)
router.post('/config', auth, canvasController.saveConfig);

// @route   GET api/canvas/config
// @desc    Get Canvas configuration status
// @access  Private
router.get('/config', auth, canvasController.getConfig);

module.exports = router;