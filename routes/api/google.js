const express = require('express');
const router = express.Router();
const { google } = require('googleapis');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const User = require('../../models/User');

/**
 * Helper function to create Google API client with user's tokens
 */
const createGoogleClient = async (userId) => {
  try {
    const user = await User.findById(userId);
    
    if (!user || !user.googleAccessToken) {
      throw new Error('User not found or missing Google tokens');
    }
    
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_CALLBACK_URL
    );
    
    oauth2Client.setCredentials({
      access_token: user.googleAccessToken,
      refresh_token: user.googleRefreshToken
    });
    
    return oauth2Client;
  } catch (err) {
    console.error('Error creating Google client:', err);
    throw err;
  }
};

/**
 * @route   GET /api/google/drive/files
 * @desc    Get user's Google Drive files
 * @access  private
 */
router.get('/drive/files', isAuthenticated, async (req, res) => {
  try {
    const auth = await createGoogleClient(req.user.id);
    const drive = google.drive({ version: 'v3', auth });
    
    // Get files from Google Drive
    const response = await drive.files.list({
      pageSize: 30,
      fields: 'files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime)',
      orderBy: 'modifiedTime desc'
    });
    
    res.json(response.data.files);
  } catch (err) {
    console.error('Error fetching Drive files:', err);
    
    if (err.message === 'User not found or missing Google tokens') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }
    
    res.status(500).json({ msg: 'Error fetching Google Drive files' });
  }
});

/**
 * @route   GET /api/google/drive/search
 * @desc    Search Google Drive files
 * @access  private
 */
router.get('/drive/search', isAuthenticated, async (req, res) => {
  try {
    const searchTerm = req.query.q;
    
    if (!searchTerm) {
      return res.status(400).json({ msg: 'Search term is required' });
    }
    
    const auth = await createGoogleClient(req.user.id);
    const drive = google.drive({ version: 'v3', auth });
    
    // Search files in Google Drive
    const response = await drive.files.list({
      q: `name contains '${searchTerm}'`,
      pageSize: 30,
      fields: 'files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime)',
      orderBy: 'modifiedTime desc'
    });
    
    res.json(response.data.files);
  } catch (err) {
    console.error('Error searching Drive files:', err);
    
    if (err.message === 'User not found or missing Google tokens') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }
    
    res.status(500).json({ msg: 'Error searching Google Drive files' });
  }
});

/**
 * @route   GET /api/google/admin/users
 * @desc    Get users from Google Admin
 * @access  private
 */
router.get('/admin/users', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const auth = await createGoogleClient(req.user.id);
    const admin = google.admin({ version: 'directory_v1', auth });
    
    // Get users from Google Admin
    const response = await admin.users.list({
      domain: process.env.ALLOWED_DOMAINS.split(',')[0],
      maxResults: 100,
      orderBy: 'email'
    });
    
    res.json(response.data.users);
  } catch (err) {
    console.error('Error fetching Admin users:', err);
    
    if (err.message === 'User not found or missing Google tokens') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }
    
    res.status(500).json({ msg: 'Error fetching Google Admin users' });
  }
});

/**
 * @route   GET /api/google/admin/groups
 * @desc    Get groups from Google Admin
 * @access  private
 */
router.get('/admin/groups', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const auth = await createGoogleClient(req.user.id);
    const admin = google.admin({ version: 'directory_v1', auth });
    
    // Get groups from Google Admin
    const response = await admin.groups.list({
      domain: process.env.ALLOWED_DOMAINS.split(',')[0],
      maxResults: 100
    });
    
    res.json(response.data.groups);
  } catch (err) {
    console.error('Error fetching Admin groups:', err);
    
    if (err.message === 'User not found or missing Google tokens') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }
    
    res.status(500).json({ msg: 'Error fetching Google Admin groups' });
  }
});

/**
 * @route   GET /api/google/admin/groups/:groupKey/members
 * @desc    Get members of a group from Google Admin
 * @access  private
 */
router.get('/admin/groups/:groupKey/members', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const auth = await createGoogleClient(req.user.id);
    const admin = google.admin({ version: 'directory_v1', auth });
    
    // Get group members from Google Admin
    const response = await admin.members.list({
      groupKey: req.params.groupKey
    });
    
    res.json(response.data.members);
  } catch (err) {
    console.error('Error fetching group members:', err);
    
    if (err.message === 'User not found or missing Google tokens') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }
    
    res.status(500).json({ msg: 'Error fetching group members' });
  }
});

module.exports = router;