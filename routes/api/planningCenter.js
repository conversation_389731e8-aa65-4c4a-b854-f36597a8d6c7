const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const { isAuthenticated } = require('../../middleware/auth');
const planningCenterController = require('../../server/controllers/planningCenterController');

// @route   GET api/planning-center/applications
// @desc    Get all Planning Center applications
// @access  Private
router.get('/applications', isAuthenticated, planningCenterController.getApplications);

// @route   GET api/planning-center/events
// @desc    Get Planning Center events
// @access  Private
router.get('/events', isAuthenticated, planningCenterController.getEvents);

// @route   GET api/planning-center/people
// @desc    Get Planning Center people
// @access  Private
router.get('/people', isAuthenticated, planningCenterController.getPeople);

// @route   GET api/planning-center/resources
// @desc    Get Planning Center resources
// @access  Private
router.get('/resources', isAuthenticated, planningCenterController.getResources);

// @route   POST api/planning-center/config
// @desc    Save Planning Center configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, planningCenterController.saveConfig);

// @route   GET api/planning-center/config
// @desc    Get Planning Center configuration status
// @access  Private
router.get('/config', isAuthenticated, planningCenterController.getConfig);

module.exports = router;
