const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const auth = require('../../middleware/auth');
const canvaController = require('../../server/controllers/canvaController');

// @route   GET api/canva/designs
// @desc    Get all Canva designs
// @access  Private
router.get('/designs', auth, canvaController.getDesigns);

// @route   GET api/canva/designs/:id
// @desc    Get Canva design details
// @access  Private
router.get('/designs/:id', auth, canvaController.getDesign);

// @route   GET api/canva/templates
// @desc    Get Canva templates
// @access  Private
router.get('/templates', auth, canvaController.getTemplates);

// @route   GET api/canva/designs/:id/assets
// @desc    Get Canva design assets
// @access  Private
router.get('/designs/:id/assets', auth, canvaController.getDesignAssets);

// @route   GET api/canva/designs/:id/files
// @desc    Get Canva design files
// @access  Private
router.get('/designs/:id/files', auth, canvaController.getDesignFiles);

// @route   GET api/canva/files/:id
// @desc    Get Canva file details
// @access  Private
router.get('/files/:id', auth, canvaController.getFile);

// @route   GET api/canva/users
// @desc    Get Canva users
// @access  Private
router.get('/users', auth, canvaController.getUsers);

// @route   GET api/canva/users/:id
// @desc    Get Canva user details
// @access  Private
router.get('/users/:id', auth, canvaController.getUser);

// @route   POST api/canva/config
// @desc    Save Canva configuration
// @access  Private (admin only)
router.post('/config', auth, canvaController.saveConfig);

// @route   GET api/canva/config
// @desc    Get Canva configuration status
// @access  Private
router.get('/config', auth, canvaController.getConfig);

module.exports = router;