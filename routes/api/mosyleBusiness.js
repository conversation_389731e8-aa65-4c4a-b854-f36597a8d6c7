const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const mosyleBusinessController = require('../../server/controllers/mosyleBusinessController');

// @route   GET api/mosyle-business/devices
// @desc    Get all Mosyle Business devices
// @access  Private
router.get('/devices', auth, mosyleBusinessController.getDevices);

// @route   GET api/mosyle-business/devices/:id
// @desc    Get Mosyle Business device details
// @access  Private
router.get('/devices/:id', auth, mosyleBusinessController.getDeviceDetails);

// @route   GET api/mosyle-business/users
// @desc    Get Mosyle Business users
// @access  Private
router.get('/users', auth, mosyleBusinessController.getUsers);

// @route   GET api/mosyle-business/groups
// @desc    Get Mosyle Business groups
// @access  Private
router.get('/groups', auth, mosyleBusinessController.getGroups);

// @route   POST api/mosyle-business/config
// @desc    Save Mosyle Business configuration
// @access  Private (admin only)
router.post('/config', auth, mosyleBusinessController.saveConfig);

// @route   GET api/mosyle-business/config
// @desc    Get Mosyle Business configuration status
// @access  Private
router.get('/config', auth, mosyleBusinessController.getConfig);

module.exports = router;