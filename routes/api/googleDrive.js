const express = require('express');
const router = express.Router();
const googleDriveController = require('../../server/controllers/googleDriveController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');

/**
 * @route   POST /api/google-drive/config
 * @desc    Save Google Drive configuration
 */
router.post('/config', isAuthenticated, hasR<PERSON><PERSON>(['admin']), googleDriveController.saveConfig);

/**
 * @route   GET /api/google-drive/config
 * @desc    Get Google Drive configuration
 */
router.get('/config', isAuthenticated, hasRoles(['admin']), googleDriveController.getConfig);

/**
 * @route   GET /api/google-drive/auth-url
 * @desc    Get Google Drive authentication URL
 */
router.get('/auth-url', isAuthenticated, hasRoles(['admin']), googleDriveController.getAuthUrl);

/**
 * @route   GET /api/google-drive/callback
 * @desc    Handle Google Drive OAuth2 callback
 */
router.get('/callback', googleDriveController.handleCallback);

/**
 * @route   GET /api/google-drive/files
 * @desc    List files in Google Drive
 */
router.get('/files', isAuthenticated, googleDriveController.listFiles);

/**
 * @route   GET /api/google-drive/search
 * @desc    Search files in Google Drive
 */
router.get('/search', isAuthenticated, googleDriveController.searchFiles);

/**
 * @route   GET /api/google-drive/files/:fileId
 * @desc    Get file metadata
 */
router.get('/files/:fileId', isAuthenticated, googleDriveController.getFile);

/**
 * @route   GET /api/google-drive/viewer/:fileId
 * @desc    Get embedded viewer URL
 */
router.get('/viewer/:fileId', isAuthenticated, googleDriveController.getEmbeddedViewerUrl);

module.exports = router;