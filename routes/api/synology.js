const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const auth = require('../../middleware/auth');
const synologyController = require('../../server/controllers/synologyController');

// @route   POST api/synology/config
// @desc    Save Synology configuration
// @access  Private (admin only)
router.post('/config', auth, (req, res) => synologyController.saveConfig(req, res));

// @route   GET api/synology/config
// @desc    Get Synology configuration status
// @access  Private
router.get('/config', auth, (req, res) => synologyController.getConfig(req, res));

// @route   GET api/synology/files
// @desc    List files in a directory
// @access  Private
router.get('/files', auth, (req, res) => synologyController.listFiles(req, res));

// @route   GET api/synology/download
// @desc    Download a file
// @access  Private
router.get('/download', auth, (req, res) => synologyController.downloadFile(req, res));

// @route   POST api/synology/share
// @desc    Create a sharing link
// @access  Private
router.post('/share', auth, (req, res) => synologyController.createSharingLink(req, res));

module.exports = router;
