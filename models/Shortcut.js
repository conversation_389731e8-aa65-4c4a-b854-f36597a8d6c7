const mongoose = require('mongoose');

const ShortcutSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  url: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'link' // Default Material-UI icon
  },
  categories: {
    type: [String],
    default: ['General']
  },
  instructions: {
    type: String
  },
  requiredRoles: {
    type: [String],
    default: ['user'] // By default, all users can see this shortcut
  },
  isPublic: {
    type: Boolean,
    default: false // If true, no login required to view
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  clickCount: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
});

// Pre-save middleware to update the updatedAt field
ShortcutSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Index for text search
ShortcutSchema.index({ 
  title: 'text', 
  description: 'text', 
  categories: 'text',
  instructions: 'text'
});

// Method to increment click count
ShortcutSchema.methods.incrementClickCount = async function() {
  this.clickCount += 1;
  return this.save();
};

// Create default shortcuts if they don't exist
ShortcutSchema.statics.createDefaultShortcuts = async function() {
  const shortcuts = [
    {
      title: 'Google Drive',
      description: 'Access church shared files and documents',
      url: 'https://drive.google.com',
      icon: 'folder',
      categories: ['Google', 'Files'],
      instructions: 'Click to access Google Drive. You may need to sign in with your church Google account.',
      isActive: true
    },
    {
      title: 'Church Calendar',
      description: 'View and manage church events',
      url: 'https://calendar.google.com',
      icon: 'event',
      categories: ['Google', 'Calendar'],
      instructions: 'Click to access the church calendar. You can add events if you have editor permissions.',
      isActive: true
    }
  ];

  for (const shortcut of shortcuts) {
    try {
      await this.findOneAndUpdate(
        { title: shortcut.title },
        shortcut,
        { upsert: true, new: true }
      );
    } catch (err) {
      console.error(`Error creating default shortcut ${shortcut.title}:`, err);
    }
  }
};

module.exports = mongoose.model('Shortcut', ShortcutSchema);