const mongoose = require('mongoose');

const CanvasConfigSchema = new mongoose.Schema({
  domain: {
    type: String,
    required: true
  },
  apiKey: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CanvasConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('canvasConfig', CanvasConfigSchema);