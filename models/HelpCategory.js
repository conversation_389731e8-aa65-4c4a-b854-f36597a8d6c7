const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Help Category Schema
 * Represents a category for help/FAQ entries
 */
const HelpCategorySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  description: {
    type: String,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    trim: true,
    unique: true,
    lowercase: true
  },
  icon: {
    type: String,
    default: 'help'
  },
  color: {
    type: String,
    default: '#1a4b8c' // CSF Blue
  },
  order: {
    type: Number,
    default: 0
  },
  parentCategory: {
    type: Schema.Types.ObjectId,
    ref: 'HelpCategory',
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Pre-save hook to generate slug if not provided
HelpCategorySchema.pre('save', function(next) {
  if (!this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  next();
});

// Virtual for getting all entries in this category
HelpCategorySchema.virtual('entries', {
  ref: 'HelpEntry',
  localField: '_id',
  foreignField: 'category',
  justOne: false
});

// Virtual for getting subcategories
HelpCategorySchema.virtual('subcategories', {
  ref: 'HelpCategory',
  localField: '_id',
  foreignField: 'parentCategory',
  justOne: false
});

module.exports = mongoose.model('HelpCategory', HelpCategorySchema);