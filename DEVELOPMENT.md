# CSF Portal Development Guide

This document outlines the development processes and guidelines for the Christian Student Fellowship (CSF) Portal project.

## Table of Contents

- [Development Environment Setup](#development-environment-setup)
- [Project Structure](#project-structure)
- [Development Workflow](#development-workflow)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Deployment](#deployment)
- [Third-Party Integrations](#third-party-integrations)

## Development Environment Setup

### Prerequisites

- Node.js (v16.x)
- npm (v8.x or higher)
- MongoDB (v4.4 or higher)
- Git

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/your-organization/csfportal.git
   cd csfportal
   ```

2. Install server dependencies:
   ```
   npm install
   ```

3. Install client dependencies:
   ```
   npm run client-install
   ```

4. Create a `.env` file in the root directory with the following variables:
   ```
   NODE_ENV=development
   PORT=5000
   MONGO_URI=mongodb://localhost:27017/csfportal
   SESSION_SECRET=your_session_secret
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback
   ```

5. Start the development server:
   ```
   npm run dev
   ```

## Project Structure

The project follows a client-server architecture:

```
csfportal/
├── client/                 # React frontend
│   ├── public/             # Static files
│   ├── src/                # React source code
│   └── package.json        # Client dependencies
├── config/                 # Configuration files
├── middleware/             # Express middleware
├── models/                 # Mongoose models
├── routes/                 # Express routes
│   └── api/                # API endpoints
├── tests/                  # Test files
├── .env                    # Environment variables (not in repo)
├── .gitignore              # Git ignore file
├── package.json            # Server dependencies
└── server.js               # Express server entry point
```

## Development Workflow

### Branching Strategy

We follow the GitFlow branching model:

- `main`: Production-ready code
- `develop`: Latest development changes
- `feature/*`: New features
- `bugfix/*`: Bug fixes
- `release/*`: Release preparation
- `hotfix/*`: Urgent production fixes

### Development Process

1. Create a new branch from `develop` for your feature or bugfix:
   ```
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit them with descriptive messages:
   ```
   git add .
   git commit -m "Add feature: description of changes"
   ```

3. Push your branch to the remote repository:
   ```
   git push -u origin feature/your-feature-name
   ```

4. Create a pull request to merge your changes into `develop`.

5. After code review and approval, your changes will be merged.

## Coding Standards

### JavaScript/React

- Use ES6+ features
- Follow the Airbnb JavaScript Style Guide
- Use functional components with hooks for React
- Use PropTypes for type checking

### CSS/Styling

- Use Material-UI components and styling system
- Follow BEM naming convention for custom CSS

### API Design

- RESTful API design principles
- Use appropriate HTTP methods (GET, POST, PUT, DELETE)
- Return consistent JSON responses
- Include proper error handling

## Testing

### Running Tests

- Run server tests: `npm test`
- Run client tests: `npm run test --prefix client`
- Run tests in watch mode: `npm run test:watch`

### Writing Tests

- Write unit tests for utility functions and components
- Write integration tests for API endpoints
- Aim for high test coverage
- See the [tests/README.md](tests/README.md) for more details

## Deployment

### Staging Deployment

Staging deployments are automatically triggered when changes are merged into the `develop` branch.

### Production Deployment

Production deployments are manually triggered after testing in staging:

1. Merge `develop` into `main`
2. Tag the release with a version number
3. CI/CD pipeline will deploy to production

## Third-Party Integrations

### Google OAuth

Used for authentication. Configuration is in `config/passport.js`.

### Planning Center

Integration for church management. See documentation in the Planning Center integration section.

### Synology

Integration for file storage. See documentation in the Synology integration section.

### Canva

Integration for graphic design. See documentation in the Canva integration section.

### GLPI

Integration for asset management.

#### Setup

1. **Prerequisites**:
   - A running GLPI instance accessible from the CSF Portal server
   - API access enabled in GLPI
   - An application token created in GLPI
   - Either a user token or username/password credentials with appropriate permissions

2. **Configuration**:
   - Navigate to the GLPI Setup page in the admin panel
   - Enter your GLPI URL (e.g., https://glpi.example.com)
   - Enter your GLPI application token
   - Choose authentication method:
     - User Token (recommended): Enter your GLPI user token
     - Username/Password: Enter your GLPI username and password
   - Click "Test Connection" to verify the connection
   - Click "Save Configuration" to save the settings

#### Usage

1. **Asset Browsing**:
   - Navigate to the GLPI Asset Browser page
   - View all assets in a list format
   - Search for assets by name or other attributes
   - Filter assets by type or manufacturer
   - Click on an asset to view its details

2. **Asset Details**:
   - View comprehensive information about an asset
   - See general information, location, ownership, and dates
   - Edit or delete the asset using the buttons in the top-right corner

3. **Asset Management**:
   - Add new assets using the "Add Asset" button in the Asset Browser
   - Edit existing assets by clicking the edit button on an asset
   - Delete assets by clicking the delete button on an asset (requires confirmation)

#### Permissions

- Regular users can view assets
- Admin users can add, edit, and delete assets
- Admin users can configure the GLPI integration

#### Troubleshooting

- If the connection test fails, verify your GLPI URL and credentials
- Ensure that the GLPI API is enabled and accessible from the CSF Portal server
- Check that your user account has the necessary permissions in GLPI
- For detailed error information, check the browser console and server logs
